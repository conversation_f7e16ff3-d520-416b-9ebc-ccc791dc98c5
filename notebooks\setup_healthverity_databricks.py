# Databricks notebook source
# MAGIC %md
# MAGIC # HealthVerity Genetic Testing Pipeline Setup
# MAGIC 
# MAGIC This notebook sets up and tests the genetic testing pipeline for your HealthVerity environment.
# MAGIC 
# MAGIC **Your Environment:**
# MAGIC - Host: `adb-3961822481655844.4.azuredatabricks.net`
# MAGIC - Warehouse: `/sql/1.0/warehouses/3b64ec91bab74a2f`
# MAGIC - Table: `healthverity_claims_sample_patient_dataset.hv_claims_sample.diagnosis`

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 0: Optional Token Setup (if needed)

# COMMAND ----------

# OPTIONAL: Only run this cell if you need to provide a Databricks token
# For most use cases running inside Databricks, this is NOT needed

# Uncomment and run if you need to provide a token:
# dbutils.widgets.text("databricks_token", "", "Databricks Token (optional)")
# token = dbutils.widgets.get("databricks_token")
# if token:
#     import os
#     os.environ['DATABRICKS_TOKEN'] = token
#     print("✅ Token set successfully")
# else:
#     print("ℹ️  No token provided - using default Databricks authentication")

print("ℹ️  Token setup complete (or skipped)")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 1: Environment Setup

# COMMAND ----------

import os
import sys
from pyspark.sql import SparkSession
from pyspark.sql.functions import *

print("🧬 Setting up Genetic Testing Pipeline for HealthVerity")
print("=" * 60)

# Set environment variables for this session
env_vars = {
    'DATABRICKS_HOST': 'adb-3961822481655844.4.azuredatabricks.net',
    'DATABRICKS_HTTP_PATH': '/sql/1.0/warehouses/3b64ec91bab74a2f',
    'CATALOG_NAME': 'healthverity_claims_sample_patient_dataset',
    'DATABASE_NAME': 'hv_claims_sample',
    'DIAGNOSIS_TABLE': 'diagnosis',
    'LOOKBACK_DAYS': '365',
    'MIN_CLAIMS_THRESHOLD': '3',
    'SIMILARITY_THRESHOLD': '0.7',
    'RISK_THRESHOLD': '0.5'
}

# Note: DATABRICKS_TOKEN not needed when running inside Databricks
# The notebook automatically has access to the workspace

print("📝 Setting environment variables...")
for key, value in env_vars.items():
    os.environ[key] = value
    print(f"   {key} = {value}")

print("\n✅ Environment variables set successfully!")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 2: Test Databricks Connection and Table Access

# COMMAND ----------

print("🔗 Testing Databricks connection and table access...")

# Test table access
table_name = "healthverity_claims_sample_patient_dataset.hv_claims_sample.diagnosis"
print(f"📊 Testing access to table: {table_name}")

try:
    df = spark.table(table_name)
    row_count = df.count()
    column_count = len(df.columns)
    
    print(f"✅ Table access successful!")
    print(f"   Rows: {row_count:,}")
    print(f"   Columns: {column_count}")
    
    # Show schema
    print(f"\n📋 Table Schema:")
    df.printSchema()
    
except Exception as e:
    print(f"❌ Failed to access table: {str(e)}")
    print("\n💡 Troubleshooting:")
    print("   1. Check if you have access to the HealthVerity catalog")
    print("   2. Verify the table name is correct")
    print("   3. Ensure your cluster has the necessary permissions")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 3: Explore Table Structure

# COMMAND ----------

if 'df' in locals():
    print("📄 Sample Data (first 5 rows):")
    df.limit(5).show(truncate=False)
    
    print(f"\n📋 All Column Names:")
    for i, col_name in enumerate(df.columns, 1):
        print(f"   {i:2d}. {col_name}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 4: Identify Potential ICD Code Columns

# COMMAND ----------

if 'df' in locals():
    print("🔍 Looking for potential ICD code columns...")
    
    # Look for columns that might contain ICD codes
    potential_icd_columns = []
    for col_name in df.columns:
        if any(keyword in col_name.lower() for keyword in ['icd', 'diagnosis', 'code', 'dx']):
            potential_icd_columns.append(col_name)
    
    print(f"📋 Potential ICD code columns found: {potential_icd_columns}")
    
    # Examine each potential column
    for col_name in potential_icd_columns:
        print(f"\n--- Analyzing column: {col_name} ---")
        
        try:
            # Show sample values
            sample_values = df.select(col_name).filter(col(col_name).isNotNull()).limit(10)
            print("Sample values:")
            sample_values.show(truncate=False)
            
            # Check for ICD-10 pattern (letter + numbers)
            icd10_pattern_count = df.filter(col(col_name).rlike("^[A-Z][0-9]{2}.*")).count()
            print(f"Records matching ICD-10 pattern: {icd10_pattern_count:,}")
            
        except Exception as e:
            print(f"Error analyzing column: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 5: Search for Genetic Disorder Patterns

# COMMAND ----------

if 'df' in locals() and potential_icd_columns:
    print("🧬 Searching for genetic disorder patterns...")
    
    # Define genetic disorder patterns
    genetic_patterns = {
        "Z8[0-4].*": "Family history codes (Z80-Z84)",
        "Q9[0-9].*": "Chromosomal abnormalities (Q90-Q99)", 
        "E7[0-9].*": "Metabolic disorders (E70-E79)",
        "Z15.*": "Genetic susceptibility to malignant neoplasm",
        "Z14.*": "Genetic carrier status"
    }
    
    total_genetic_found = 0
    
    for col_name in potential_icd_columns:
        print(f"\n--- Genetic patterns in column: {col_name} ---")
        
        col_genetic_total = 0
        for pattern, description in genetic_patterns.items():
            try:
                pattern_count = df.filter(col(col_name).rlike(pattern)).count()
                print(f"   {pattern}: {pattern_count:,} records ({description})")
                col_genetic_total += pattern_count
            except Exception as e:
                print(f"   {pattern}: Error - {str(e)}")
        
        print(f"   Total genetic patterns in {col_name}: {col_genetic_total:,}")
        total_genetic_found += col_genetic_total
    
    print(f"\n🧬 TOTAL GENETIC PATTERNS FOUND: {total_genetic_found:,}")
    
    if total_genetic_found > 0:
        print("✅ Genetic disorder codes detected! Ready for patient identification.")
    else:
        print("⚠️  No genetic patterns found with standard search.")
        print("💡 This could mean:")
        print("   1. ICD codes are in a different format")
        print("   2. Genetic codes use different patterns")
        print("   3. Need to check other columns")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 6: Identify Patient ID Column

# COMMAND ----------

if 'df' in locals():
    print("👥 Looking for patient ID column...")
    
    # Look for patient ID columns
    potential_patient_columns = []
    for col_name in df.columns:
        if any(keyword in col_name.lower() for keyword in ['patient', 'ptid', 'member', 'person']):
            potential_patient_columns.append(col_name)
    
    print(f"📋 Potential patient ID columns: {potential_patient_columns}")
    
    for col_name in potential_patient_columns:
        print(f"\n--- Analyzing column: {col_name} ---")
        try:
            unique_count = df.select(col_name).distinct().count()
            total_count = df.count()
            sample_values = df.select(col_name).limit(5).collect()
            
            print(f"   Unique values: {unique_count:,}")
            print(f"   Total records: {total_count:,}")
            print(f"   Uniqueness ratio: {unique_count/total_count:.2%}")
            print("   Sample values:")
            for row in sample_values:
                print(f"     {row[0]}")
                
        except Exception as e:
            print(f"   Error: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 7: Look for Date Columns

# COMMAND ----------

if 'df' in locals():
    print("📅 Looking for date columns...")
    
    # Look for date columns
    date_columns = []
    for col_name in df.columns:
        if any(keyword in col_name.lower() for keyword in ['date', 'time', 'day']):
            date_columns.append(col_name)
    
    print(f"📋 Potential date columns: {date_columns}")
    
    for col_name in date_columns:
        print(f"\n--- Analyzing column: {col_name} ---")
        try:
            # Get date range
            date_stats = df.select(
                min(col(col_name)).alias("min_date"),
                max(col(col_name)).alias("max_date"),
                count(col(col_name)).alias("non_null_count")
            ).collect()[0]
            
            print(f"   Date range: {date_stats.min_date} to {date_stats.max_date}")
            print(f"   Non-null records: {date_stats.non_null_count:,}")
            
        except Exception as e:
            print(f"   Error: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 8: Generate Configuration Recommendations

# COMMAND ----------

print("🔧 Configuration Recommendations")
print("=" * 40)

# Based on the analysis above, provide recommendations
print("\nBased on the table exploration, update these mappings:")

print("\n# Column Mappings (UPDATE BASED ON FINDINGS ABOVE)")
if 'potential_patient_columns' in locals() and potential_patient_columns:
    print(f"PATIENT_ID_COLUMN = '{potential_patient_columns[0]}'  # Most likely patient ID column")
else:
    print("PATIENT_ID_COLUMN = 'patient_id'  # UPDATE THIS")

if 'potential_icd_columns' in locals() and potential_icd_columns:
    print(f"ICD_CODE_COLUMN = '{potential_icd_columns[0]}'  # Most likely ICD code column")
else:
    print("ICD_CODE_COLUMN = 'icd_code'  # UPDATE THIS")

if 'date_columns' in locals() and date_columns:
    print(f"DATE_COLUMN = '{date_columns[0]}'  # Most likely date column")
else:
    print("DATE_COLUMN = 'diagnosis_date'  # UPDATE THIS")

print(f"\n# Table Configuration")
print(f"TABLE_NAME = '{table_name}'")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 9: Test Genetic Patient Identification (if patterns found)

# COMMAND ----------

if 'total_genetic_found' in locals() and total_genetic_found > 0:
    print("🧬 Testing genetic patient identification...")
    
    # Use the most promising ICD column
    icd_column = potential_icd_columns[0] if potential_icd_columns else 'icd_code'
    patient_column = potential_patient_columns[0] if potential_patient_columns else 'patient_id'
    
    print(f"Using ICD column: {icd_column}")
    print(f"Using Patient column: {patient_column}")
    
    try:
        # Find patients with genetic diagnoses
        genetic_filter = "^(Z8[0-4]|Q9[0-9]|E7[0-9]|Z15|Z14)"
        
        genetic_patients = (
            df.filter(col(icd_column).rlike(genetic_filter))
            .groupBy(patient_column)
            .agg(
                count("*").alias("genetic_claim_count"),
                collect_set(icd_column).alias("genetic_icd_codes")
            )
            .filter(col("genetic_claim_count") >= 1)
        )
        
        genetic_count = genetic_patients.count()
        print(f"✅ Found {genetic_count:,} patients with genetic diagnoses!")
        
        if genetic_count > 0:
            print("\n📋 Sample genetic patients:")
            genetic_patients.limit(10).show(truncate=False)
            
            # Show most common genetic codes
            genetic_codes = (
                genetic_patients
                .select(explode("genetic_icd_codes").alias("icd_code"))
                .groupBy("icd_code")
                .count()
                .orderBy(desc("count"))
                .limit(10)
            )
            
            print("\n📊 Most common genetic ICD codes:")
            genetic_codes.show()
        
    except Exception as e:
        print(f"❌ Error in genetic patient identification: {str(e)}")

else:
    print("⚠️  Skipping genetic patient identification - no genetic patterns found")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 10: Summary and Next Steps

# COMMAND ----------

print("📊 SETUP SUMMARY")
print("=" * 30)

if 'df' in locals():
    print(f"✅ Successfully connected to HealthVerity table")
    print(f"   Total records: {df.count():,}")
    print(f"   Total columns: {len(df.columns)}")
else:
    print("❌ Failed to connect to HealthVerity table")

if 'total_genetic_found' in locals():
    print(f"🧬 Genetic patterns found: {total_genetic_found:,}")
    if total_genetic_found > 0:
        print("✅ Ready for genetic testing pipeline!")
    else:
        print("⚠️  May need to adjust search patterns")

print(f"\n🎯 NEXT STEPS:")
print("=" * 15)

if 'total_genetic_found' in locals() and total_genetic_found > 0:
    print("1. ✅ Data connection successful")
    print("2. ✅ Genetic patterns detected") 
    print("3. 🔄 Update column mappings in the pipeline code")
    print("4. 🚀 Run the full genetic testing pipeline")
    print("5. 📊 Generate patient recommendations")
else:
    print("1. ✅ Data connection successful")
    print("2. ⚠️  Review column mappings")
    print("3. 🔍 Run detailed data exploration")
    print("4. 🔧 Adjust genetic code search patterns")
    print("5. 🔄 Re-run setup after adjustments")

print(f"\n📚 Resources:")
print("   → notebooks/healthverity_data_exploration.py - Detailed exploration")
print("   → examples/healthverity_genetic_testing_example.py - Working example")
print("   → docs/API_REFERENCE.md - Complete API documentation")

print(f"\n🎉 Setup completed!")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration Export
# MAGIC 
# MAGIC Copy the configuration below and save it for use in your pipeline:

# COMMAND ----------

print("📋 CONFIGURATION FOR GENETIC TESTING PIPELINE")
print("=" * 50)

config_output = f"""
# HealthVerity Configuration
DATABRICKS_HOST = "adb-3961822481655844.4.azuredatabricks.net"
DATABRICKS_HTTP_PATH = "/sql/1.0/warehouses/3b64ec91bab74a2f"
CATALOG_NAME = "healthverity_claims_sample_patient_dataset"
DATABASE_NAME = "hv_claims_sample"
TABLE_NAME = "diagnosis"

# Column Mappings (UPDATE BASED ON EXPLORATION ABOVE)
"""

if 'potential_patient_columns' in locals() and potential_patient_columns:
    config_output += f'PATIENT_ID_COLUMN = "{potential_patient_columns[0]}"\n'
else:
    config_output += 'PATIENT_ID_COLUMN = "patient_id"  # UPDATE THIS\n'

if 'potential_icd_columns' in locals() and potential_icd_columns:
    config_output += f'ICD_CODE_COLUMN = "{potential_icd_columns[0]}"\n'
else:
    config_output += 'ICD_CODE_COLUMN = "icd_code"  # UPDATE THIS\n'

if 'date_columns' in locals() and date_columns:
    config_output += f'DATE_COLUMN = "{date_columns[0]}"\n'
else:
    config_output += 'DATE_COLUMN = "diagnosis_date"  # UPDATE THIS\n'

config_output += """
# Processing Parameters
LOOKBACK_DAYS = 365
MIN_CLAIMS_THRESHOLD = 3
SIMILARITY_THRESHOLD = 0.7
RISK_THRESHOLD = 0.5
"""

print(config_output)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Ready to Proceed!
# MAGIC 
# MAGIC Your HealthVerity genetic testing pipeline setup is now complete. 
# MAGIC 
# MAGIC **What we accomplished:**
# MAGIC 1. ✅ Connected to your HealthVerity Databricks environment
# MAGIC 2. ✅ Explored your diagnosis table structure  
# MAGIC 3. ✅ Identified potential ICD code and patient ID columns
# MAGIC 4. ✅ Searched for genetic disorder patterns
# MAGIC 5. ✅ Generated configuration recommendations
# MAGIC 6. ✅ Tested basic genetic patient identification (if patterns found)
# MAGIC 
# MAGIC **Next steps:**
# MAGIC 1. Update the column mappings in your pipeline code based on findings above
# MAGIC 2. Run the detailed exploration notebook for deeper analysis
# MAGIC 3. Execute the full genetic testing pipeline to identify candidates
# MAGIC 
# MAGIC You're now ready to identify patients who may benefit from genetic testing! 🧬
