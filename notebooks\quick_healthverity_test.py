# Databricks notebook source
# MAGIC %md
# MAGIC # Quick HealthVerity Connection Test
# MAGIC 
# MAGIC This is a simplified test to quickly verify your HealthVerity table access and find genetic patterns.
# MAGIC **No token required** - just run in your Databricks workspace!

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *

print("🧬 Quick HealthVerity Genetic Testing Setup")
print("=" * 45)

# COMMAND ----------

# Test table access
table_name = "healthverity_claims_sample_patient_dataset.hv_claims_sample.diagnosis"
print(f"📊 Testing access to: {table_name}")

try:
    df = spark.table(table_name)
    row_count = df.count()
    column_count = len(df.columns)
    
    print(f"✅ SUCCESS! Table loaded successfully")
    print(f"   📊 Rows: {row_count:,}")
    print(f"   📋 Columns: {column_count}")
    
    print(f"\n📋 Column Names:")
    for i, col_name in enumerate(df.columns, 1):
        print(f"   {i:2d}. {col_name}")
    
except Exception as e:
    print(f"❌ FAILED to access table: {str(e)}")
    print("\n💡 Possible solutions:")
    print("   1. Check if you have access to the HealthVerity catalog")
    print("   2. Verify your cluster has the necessary permissions")
    print("   3. Try a different table name if this one is incorrect")

# COMMAND ----------

# Show sample data
if 'df' in locals():
    print("📄 Sample Data (first 3 rows):")
    df.limit(3).show(truncate=False)

# COMMAND ----------

# Look for ICD code columns
if 'df' in locals():
    print("🔍 Looking for ICD code columns...")
    
    icd_columns = []
    for col_name in df.columns:
        if any(keyword in col_name.lower() for keyword in ['icd', 'diagnosis', 'code', 'dx']):
            icd_columns.append(col_name)
    
    print(f"📋 Potential ICD columns: {icd_columns}")
    
    # Test each column for genetic patterns
    genetic_patterns = ["Z8[0-4].*", "Q9[0-9].*", "E7[0-9].*"]
    
    for col_name in icd_columns:
        print(f"\n--- Testing column: {col_name} ---")
        try:
            # Show sample values
            samples = df.select(col_name).filter(col(col_name).isNotNull()).limit(5).collect()
            print("Sample values:")
            for row in samples:
                print(f"   {row[0]}")
            
            # Test genetic patterns
            total_genetic = 0
            for pattern in genetic_patterns:
                count = df.filter(col(col_name).rlike(pattern)).count()
                print(f"   Pattern {pattern}: {count:,} matches")
                total_genetic += count
            
            print(f"   🧬 Total genetic patterns: {total_genetic:,}")
            
        except Exception as e:
            print(f"   Error: {str(e)}")

# COMMAND ----------

# Look for patient ID columns
if 'df' in locals():
    print("👥 Looking for patient ID columns...")
    
    patient_columns = []
    for col_name in df.columns:
        if any(keyword in col_name.lower() for keyword in ['patient', 'ptid', 'member', 'person']):
            patient_columns.append(col_name)
    
    print(f"📋 Potential patient columns: {patient_columns}")
    
    for col_name in patient_columns:
        try:
            unique_count = df.select(col_name).distinct().count()
            total_count = df.count()
            print(f"   {col_name}: {unique_count:,} unique values ({unique_count/total_count:.1%} unique)")
        except:
            print(f"   {col_name}: Error analyzing")

# COMMAND ----------

# Summary and recommendations
print("\n📊 QUICK TEST SUMMARY")
print("=" * 25)

if 'df' in locals():
    print("✅ Table access: SUCCESS")
    
    if 'icd_columns' in locals() and icd_columns:
        print(f"✅ ICD columns found: {len(icd_columns)}")
        print(f"   Recommended ICD column: {icd_columns[0]}")
    else:
        print("⚠️  No obvious ICD columns found")
    
    if 'patient_columns' in locals() and patient_columns:
        print(f"✅ Patient columns found: {len(patient_columns)}")
        print(f"   Recommended patient column: {patient_columns[0]}")
    else:
        print("⚠️  No obvious patient columns found")
    
    # Check if we found genetic patterns
    genetic_found = False
    if 'icd_columns' in locals():
        for col_name in icd_columns:
            try:
                test_count = df.filter(col(col_name).rlike("^(Z8[0-4]|Q9[0-9]|E7[0-9])")).count()
                if test_count > 0:
                    genetic_found = True
                    print(f"🧬 Genetic patterns found in {col_name}: {test_count:,}")
                    break
            except:
                pass
    
    if genetic_found:
        print("🎉 READY FOR GENETIC TESTING PIPELINE!")
        print("\nNext steps:")
        print("1. Run the full setup notebook")
        print("2. Configure column mappings")
        print("3. Execute genetic patient identification")
    else:
        print("⚠️  No genetic patterns detected")
        print("\nNext steps:")
        print("1. Check column mappings")
        print("2. Review ICD code formats")
        print("3. Run detailed exploration")

else:
    print("❌ Table access: FAILED")
    print("Fix table access before proceeding")

print(f"\n📚 Next: Run the full setup notebook for detailed analysis")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Results Summary
# MAGIC 
# MAGIC This quick test checked:
# MAGIC - ✅ Table connectivity 
# MAGIC - 🔍 Column identification
# MAGIC - 🧬 Genetic pattern detection
# MAGIC - 👥 Patient ID detection
# MAGIC 
# MAGIC **If successful**, you're ready to run the full genetic testing pipeline!
# MAGIC 
# MAGIC **If issues found**, use the recommendations above to troubleshoot.
