{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "4f5c8a5e-8b2a-4c8f-9c5a-1d2e3f4g5h6i", "showTitle": false, "title": ""}}, "source": ["# Neptune Enhanced Genetic Testing Pipeline\n", "\n", "**Multi-Table Comprehensive Analysis**\n", "\n", "This enhanced pipeline leverages all available Neptune data tables:\n", "- **Diagnosis Table:** Genetic disorder ICD codes\n", "- **Medical Claims:** Healthcare utilization patterns  \n", "- **Pharmacy Claims:** Medication patterns and adherence\n", "\n", "**Result:** More accurate genetic testing candidate identification through comprehensive patient profiling."]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "1a2b3c4d-5e6f-7g8h-9i0j-1k2l3m4n5o6p", "showTitle": false, "title": ""}}, "source": ["## 🔧 Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3a94ad63-fdac-4d5f-827a-fb366b185858", "showTitle": false, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "\n", "# Neptune Multi-Table Configuration (using HealthVerity data source)\n", "CATALOG = \"healthverity_claims_sample_patient_dataset\"\n", "DATABASE = \"hv_claims_sample\"\n", "\n", "TABLES = {\n", "    \"diagnosis\": f\"{CATALOG}.{DATABASE}.diagnosis\",\n", "    \"medical_claim\": f\"{CATALOG}.{DATABASE}.medical_claim\",\n", "    \"pharmacy_claim\": f\"{CATALOG}.{DATABASE}.pharmacy_claim\"\n", "}\n", "\n", "# Pipeline Parameters\n", "LOOKBACK_DAYS = 365\n", "MIN_GENETIC_CLAIMS = 1\n", "MIN_CANDIDATE_CLAIMS = 5\n", "SIMILARITY_THRESHOLD = 0.7\n", "COMPREHENSIVE_THRESHOLD = 0.6  # Lower threshold for multi-dimensional analysis\n", "\n", "print(\"🧬 Neptune Enhanced Genetic Testing Pipeline\")\n", "print(\"=\" * 55)\n", "print(f\"📊 Multi-Table Configuration:\")\n", "for name, path in TABLES.items():\n", "    print(f\"   {name}: {path}\")\n", "print(f\"\\n⚙️  Enhanced Parameters:\")\n", "print(f\"   Lookback period: {LOOKBACK_DAYS} days\")\n", "print(f\"   Comprehensive similarity threshold: {COMPREHENSIVE_THRESHOLD}\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "2b3c4d5e-6f7g-8h9i-0j1k-2l3m4n5o6p7q", "showTitle": false, "title": ""}}, "source": ["## 📊 Step 1: <PERSON><PERSON> and Validate All Tables"]}, {"cell_type": "code", "execution_count": null, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4c5d6e7f-8g9h-0i1j-2k3l-4m5n6o7p8q9r", "showTitle": false, "title": ""}}, "outputs": [], "source": ["print(\"📊 Loading all Neptune data tables...\")\n", "\n", "# Load tables with error handling\n", "table_data = {}\n", "table_stats = {}\n", "\n", "for table_name, table_path in TABLES.items():\n", "    try:\n", "        df = spark.table(table_path)\n", "        row_count = df.count()\n", "        table_data[table_name] = df\n", "        table_stats[table_name] = {\n", "            'rows': row_count,\n", "            'columns': len(df.columns),\n", "            'available': True\n", "        }\n", "        print(f\"✅ {table_name}: {row_count:,} records, {len(df.columns)} columns\")\n", "    except Exception as e:\n", "        table_stats[table_name] = {'available': False, 'error': str(e)}\n", "        print(f\"❌ {table_name}: Error - {str(e)}\")\n", "\n", "# Determine available tables\n", "available_tables = [name for name, stats in table_stats.items() if stats.get('available', False)]\n", "print(f\"\\n✅ Available tables: {available_tables}\")\n", "\n", "if 'diagnosis' not in available_tables:\n", "    raise ValueError(\"Diagnosis table is required but not available\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "5d6e7f8g-9h0i-1j2k-3l4m-5n6o7p8q9r0s", "showTitle": false, "title": ""}}, "source": ["## 🧬 Step 2: Comprehensive Genetic Patient Identification"]}, {"cell_type": "code", "execution_count": null, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6e7f8g9h-0i1j-2k3l-4m5n-6o7p8q9r0s1t", "showTitle": false, "title": ""}}, "outputs": [], "source": ["print(\"🧬 Comprehensive genetic patient identification...\")\n", "\n", "# Primary: Genetic disorders from diagnosis table\n", "diagnosis_df = table_data['diagnosis']\n", "\n", "# Enhanced genetic disorder patterns\n", "genetic_patterns = {\n", "    # Family history (Z80-Z84)\n", "    \"Z80\": \"Family history of malignant neoplasm of digestive organs\",\n", "    \"Z81\": \"Family history of malignant neoplasm of trachea, bronchus and lung\",\n", "    \"Z82\": \"Family history of certain disabilities and chronic diseases\", \n", "    \"Z83\": \"Family history of other specific disorders\",\n", "    \"Z84\": \"Family history of other conditions\",\n", "    \n", "    # Chromosomal abnormalities (Q90-Q99)\n", "    \"Q90\": \"Down syndrome\", \"Q91\": \"<PERSON> syndrome and <PERSON><PERSON> syndrome\",\n", "    \"Q92\": \"Other trisomies\", \"Q93\": \"Monosomies and deletions\",\n", "    \"Q95\": \"Balanced rearrangements\", \"Q96\": \"Turner syndrome\",\n", "    \"Q97\": \"Other sex chromosome abnormalities, female\",\n", "    \"Q98\": \"Other sex chromosome abnormalities, male\", \n", "    \"Q99\": \"Other chromosome abnormalities\",\n", "    \n", "    # Metabolic disorders (E70-E88)\n", "    \"E70\": \"Disorders of aromatic amino-acid metabolism\",\n", "    \"E71\": \"Disorders of branched-chain amino-acid metabolism\",\n", "    \"E72\": \"Other disorders of amino-acid metabolism\",\n", "    \"E74\": \"Other disorders of carbohydrate metabolism\",\n", "    \"E75\": \"Disorders of sphingolipid metabolism\",\n", "    \"E76\": \"Disorders of glycosaminoglycan metabolism\",\n", "    \"E77\": \"Disorders of glycoprotein metabolism\",\n", "    \"E78\": \"Disorders of lipoprotein metabolism\",\n", "    \"E79\": \"Disorders of purine and pyrimidine metabolism\",\n", "    \n", "    # Additional genetic indicators\n", "    \"Z15\": \"Genetic susceptibility to malignant neoplasm\",\n", "    \"Z14\": \"Genetic carrier status\",\n", "    \"Q87\": \"Other specified congenital malformation syndromes\"\n", "}\n", "\n", "# Find genetic patients\n", "genetic_codes = list(genetic_patterns.keys())\n", "genetic_filter = \"|\".join([f\"^{code}\" for code in genetic_codes])\n", "\n", "genetic_patients = (\n", "    diagnosis_df\n", "    .filter(col(\"diagnosis_code\").rlike(genetic_filter))\n", "    .groupBy(\"patient_id\")\n", "    .agg(\n", "        count(\"*\").alias(\"genetic_claim_count\"),\n", "        collect_set(\"diagnosis_code\").alias(\"genetic_icd_codes\"),\n", "        min(\"date_service\").alias(\"first_genetic_diagnosis\"),\n", "        max(\"date_service\").alias(\"last_genetic_diagnosis\"),\n", "        countDistinct(\"diagnosis_code\").alias(\"unique_genetic_codes\")\n", "    )\n", "    .filter(col(\"genetic_claim_count\") >= MIN_GENETIC_CLAIMS)\n", ")\n", "\n", "genetic_patient_count = genetic_patients.count()\n", "print(f\"🧬 Found {genetic_patient_count:,} patients with genetic diagnoses\")\n", "\n", "if genetic_patient_count == 0:\n", "    print(\"❌ No genetic patients found. Cannot proceed with pipeline.\")\n", "    dbutils.notebook.exit(\"No genetic patterns detected\")\n", "\n", "# Cache genetic patients\n", "genetic_patients.cache()\n", "\n", "# Get genetic patient IDs for filtering\n", "genetic_patient_ids = [row.patient_id for row in genetic_patients.select(\"patient_id\").collect()]"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "7f8g9h0i-1j2k-3l4m-5n6o-7p8q9r0s1t2u", "showTitle": false, "title": ""}}, "source": ["## 📈 Step 3: Multi-Dimensional Patient Profiling"]}, {"cell_type": "code", "execution_count": null, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8g9h0i1j-2k3l-4m5n-6o7p-8q9r0s1t2u3v", "showTitle": false, "title": ""}}, "outputs": [], "source": ["print(\"📈 Building comprehensive patient profiles...\")\n", "\n", "# 1. DIAGNOSIS UTILIZATION PROFILE\n", "print(\"\\n📊 Building diagnosis utilization profiles...\")\n", "\n", "diagnosis_profiles = (\n", "    diagnosis_df\n", "    .groupBy(\"patient_id\")\n", "    .agg(\n", "        count(\"*\").alias(\"total_diagnoses\"),\n", "        countDistinct(\"diagnosis_code\").alias(\"unique_diagnoses\"),\n", "        countDistinct(\"date_service\").alias(\"diagnosis_service_days\"),\n", "        min(\"date_service\").alias(\"first_diagnosis\"),\n", "        max(\"date_service\").alias(\"last_diagnosis\")\n", "    )\n", ")\n", "\n", "print(f\"   Diagnosis profiles: {diagnosis_profiles.count():,} patients\")\n", "\n", "# 2. MEDICAL CLAIMS UTILIZATION PROFILE (if available)\n", "medical_profiles = None\n", "if 'medical_claim' in available_tables:\n", "    print(\"\\n🏥 Building medical claims utilization profiles...\")\n", "    \n", "    medical_df = table_data['medical_claim']\n", "    \n", "    medical_profiles = (\n", "        medical_df\n", "        .groupBy(\"patient_id\")\n", "        .agg(\n", "            count(\"*\").alias(\"total_medical_claims\"),\n", "            countDistinct(\"claim_id\").alias(\"unique_medical_claims\"),\n", "            countDistinct(\"date_service\").alias(\"medical_service_days\"),\n", "            countDistinct(\"location_of_care\").alias(\"unique_care_locations\"),\n", "            countDistinct(\"pay_type\").alias(\"unique_pay_types\"),\n", "            min(\"date_service\").alias(\"first_medical_service\"),\n", "            max(\"date_service\").alias(\"last_medical_service\")\n", "        )\n", "    )\n", "    \n", "    print(f\"   Medical profiles: {medical_profiles.count():,} patients\")\n", "\n", "# 3. PHARMACY UTILIZATION PROFILE (if available)\n", "pharmacy_profiles = None\n", "if 'pharmacy_claim' in available_tables:\n", "    print(\"\\n💊 Building pharmacy utilization profiles...\")\n", "    \n", "    pharmacy_df = table_data['pharmacy_claim']\n", "    \n", "    pharmacy_profiles = (\n", "        pharmacy_df\n", "        .groupBy(\"patient_id\")\n", "        .agg(\n", "            count(\"*\").alias(\"total_pharmacy_claims\"),\n", "            countDistinct(\"claim_id\").alias(\"unique_pharmacy_claims\"),\n", "            countDistinct(\"ndc_code\").alias(\"unique_medications\"),\n", "            sum(\"quantity\").alias(\"total_medication_quantity\"),\n", "            sum(\"days_supply\").alias(\"total_days_supply\"),\n", "            avg(\"days_supply\").alias(\"avg_days_supply\"),\n", "            min(\"date_service\").alias(\"first_pharmacy_service\"),\n", "            max(\"date_service\").alias(\"last_pharmacy_service\")\n", "        )\n", "    )\n", "    \n", "    print(f\"   Pharmacy profiles: {pharmacy_profiles.count():,} patients\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "neptune_enhanced_genetic_pipeline", "widgets": {}}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}