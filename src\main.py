"""
Main pipeline orchestration for genetic testing ML system.
Coordinates all components to identify patients for genetic testing recommendations.
"""

import logging
import sys
from typing import Dict, Any, Optional
from datetime import datetime
from pyspark.sql import SparkSession

# Import pipeline components
from data.loaders import DataLoader
from data.processors import ClaimsProcessor
from models.icd_classifier import ICD10GeneticClassifier
from models.profile_builder import PatientProfileBuilder
from models.similarity import SimilarityMatcher
from models.recommender import GeneticRiskScorer, GeneticTestingRecommendationEngine
from utils.config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class GeneticTestingPipeline:
    """Main pipeline for genetic testing patient identification."""
    
    def __init__(self):
        self.config = get_config()
        self.spark = self._initialize_spark()
        
        # Initialize components
        self.data_loader = DataLoader(self.spark)
        self.claims_processor = ClaimsProcessor(self.spark)
        self.icd_classifier = ICD10GeneticClassifier(self.spark)
        self.profile_builder = PatientProfileBuilder(self.spark)
        self.similarity_matcher = SimilarityMatcher(self.spark)
        self.risk_scorer = GeneticRiskScorer(self.spark)
        self.recommendation_engine = GeneticTestingRecommendationEngine(self.spark)
        
    def _initialize_spark(self) -> SparkSession:
        """Initialize Spark session with optimized configuration."""
        
        spark_config = self.config.get_spark_config()
        
        builder = SparkSession.builder.appName("GeneticTestingML")
        
        # Apply configuration
        for key, value in spark_config.items():
            builder = builder.config(key, value)
        
        spark = builder.getOrCreate()
        spark.sparkContext.setLogLevel("WARN")
        
        logger.info("Spark session initialized")
        return spark
    
    def run_full_pipeline(self, 
                         lookback_days: Optional[int] = None,
                         max_recommendations: int = 1000) -> Dict[str, Any]:
        """
        Run the complete genetic testing identification pipeline.
        
        Args:
            lookback_days: Number of days to look back for claims data
            max_recommendations: Maximum number of recommendations to generate
            
        Returns:
            Dictionary with pipeline results and metrics
        """
        logger.info("Starting genetic testing identification pipeline")
        
        start_time = datetime.now()
        
        try:
            # Step 1: Load and process data
            logger.info("Step 1: Loading and processing data")
            data_results = self._load_and_process_data(lookback_days)
            
            # Step 2: Identify genetic patients
            logger.info("Step 2: Identifying genetic disorder patients")
            genetic_results = self._identify_genetic_patients(data_results)
            
            # Step 3: Build patient profiles
            logger.info("Step 3: Building patient profiles")
            profile_results = self._build_patient_profiles(genetic_results, data_results)
            
            # Step 4: Find similar patients
            logger.info("Step 4: Finding similar undiagnosed patients")
            similarity_results = self._find_similar_patients(profile_results, data_results)
            
            # Step 5: Score and rank patients
            logger.info("Step 5: Scoring and ranking patients")
            scoring_results = self._score_and_rank_patients(
                genetic_results, similarity_results, data_results
            )
            
            # Step 6: Generate recommendations
            logger.info("Step 6: Generating recommendations")
            recommendation_results = self._generate_recommendations(
                scoring_results, similarity_results, data_results, max_recommendations
            )
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # Compile final results
            pipeline_results = {
                "execution_time_seconds": execution_time,
                "data_summary": data_results["summary"],
                "genetic_patients_identified": genetic_results["count"],
                "similar_patients_found": similarity_results["count"],
                "recommendations_generated": recommendation_results["count"],
                "top_recommendations": recommendation_results["top_10"],
                "model_performance": scoring_results.get("model_performance", {}),
                "pipeline_status": "completed_successfully"
            }
            
            logger.info(f"Pipeline completed successfully in {execution_time:.2f} seconds")
            logger.info(f"Generated {recommendation_results['count']} recommendations")
            
            return pipeline_results
            
        except Exception as e:
            logger.error(f"Pipeline failed: {str(e)}")
            return {
                "pipeline_status": "failed",
                "error_message": str(e),
                "execution_time_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    def _load_and_process_data(self, lookback_days: Optional[int]) -> Dict[str, Any]:
        """Load and process all required data."""
        
        if lookback_days is None:
            lookback_days = self.config.settings.lookback_days
        
        # Load raw data
        patients_df = self.data_loader.load_patients()
        medical_claims_df = self.data_loader.load_medical_claims()
        pharmacy_claims_df = self.data_loader.load_pharmacy_claims()
        
        # Process claims data
        processed_medical = self.claims_processor.process_medical_claims(medical_claims_df)
        processed_pharmacy = self.claims_processor.process_pharmacy_claims(pharmacy_claims_df)
        
        # Aggregate patient features
        patient_features_df = self.claims_processor.aggregate_patient_features(
            processed_medical, processed_pharmacy, lookback_days
        )
        
        return {
            "patients": patients_df,
            "medical_claims": processed_medical,
            "pharmacy_claims": processed_pharmacy,
            "patient_features": patient_features_df,
            "summary": {
                "total_patients": patients_df.count(),
                "total_medical_claims": processed_medical.count(),
                "total_pharmacy_claims": processed_pharmacy.count(),
                "patients_with_features": patient_features_df.count()
            }
        }
    
    def _identify_genetic_patients(self, data_results: Dict[str, Any]) -> Dict[str, Any]:
        """Identify patients with genetic disorder diagnoses."""
        
        genetic_patients_df = self.icd_classifier.identify_genetic_patients(
            data_results["medical_claims"],
            min_genetic_claims=self.config.settings.min_claims_threshold
        )
        
        return {
            "genetic_patients": genetic_patients_df,
            "count": genetic_patients_df.count()
        }
    
    def _build_patient_profiles(self, 
                               genetic_results: Dict[str, Any],
                               data_results: Dict[str, Any]) -> Dict[str, Any]:
        """Build comprehensive patient profiles."""
        
        genetic_profiles_df = self.profile_builder.build_genetic_patient_profiles(
            genetic_results["genetic_patients"],
            data_results["patient_features"]
        )
        
        # Analyze common patterns
        common_patterns = self.profile_builder.identify_common_patterns(genetic_profiles_df)
        
        return {
            "genetic_profiles": genetic_profiles_df,
            "common_patterns": common_patterns,
            "count": genetic_profiles_df.count()
        }
    
    def _find_similar_patients(self,
                              profile_results: Dict[str, Any],
                              data_results: Dict[str, Any]) -> Dict[str, Any]:
        """Find undiagnosed patients similar to genetic patients."""
        
        similarity_method = self.config.model_config.similarity_method
        
        similar_patients_df = self.similarity_matcher.find_similar_undiagnosed_patients(
            profile_results["genetic_profiles"],
            data_results["patient_features"],
            similarity_method=similarity_method
        )
        
        return {
            "similar_patients": similar_patients_df,
            "count": similar_patients_df.count()
        }
    
    def _score_and_rank_patients(self,
                                genetic_results: Dict[str, Any],
                                similarity_results: Dict[str, Any],
                                data_results: Dict[str, Any]) -> Dict[str, Any]:
        """Score and rank patients for genetic testing risk."""
        
        # Create control group (random sample of non-genetic patients)
        genetic_patient_ids = [row.patient_id for row in 
                              genetic_results["genetic_patients"].select("patient_id").collect()]
        
        control_patients_df = (
            data_results["patient_features"]
            .filter(~col("patient_id").isin(genetic_patient_ids))
            .sample(0.1)  # Sample 10% for control group
        )
        
        # Train risk model
        model_results = self.risk_scorer.train_risk_model(
            genetic_results["genetic_patients"],
            control_patients_df,
            data_results["patient_features"]
        )
        
        # Score similar patients
        risk_scores_df = self.risk_scorer.score_patients(
            similarity_results["similar_patients"].join(
                data_results["patient_features"],
                similarity_results["similar_patients"]["undiagnosed_patient_id"] == 
                data_results["patient_features"]["patient_id"],
                "inner"
            )
        )
        
        return {
            "risk_scores": risk_scores_df,
            "model_performance": model_results,
            "count": risk_scores_df.count()
        }
    
    def _generate_recommendations(self,
                                 scoring_results: Dict[str, Any],
                                 similarity_results: Dict[str, Any],
                                 data_results: Dict[str, Any],
                                 max_recommendations: int) -> Dict[str, Any]:
        """Generate final genetic testing recommendations."""
        
        recommendations_df = self.recommendation_engine.generate_recommendations(
            scoring_results["risk_scores"],
            similarity_results["similar_patients"],
            data_results["patient_features"]
        )
        
        # Limit to top recommendations
        top_recommendations_df = (
            recommendations_df
            .orderBy(desc("priority_score"))
            .limit(max_recommendations)
        )
        
        # Create summary
        summary = self.recommendation_engine.create_recommendation_summary(
            top_recommendations_df
        )
        
        return {
            "recommendations": top_recommendations_df,
            "summary": summary,
            "count": top_recommendations_df.count(),
            "top_10": summary.get("top_10_patients", [])
        }
    
    def save_results(self, 
                    pipeline_results: Dict[str, Any],
                    output_path: str) -> None:
        """Save pipeline results to specified location."""
        
        if "recommendations" in pipeline_results:
            # Save recommendations
            self.recommendation_engine.export_recommendations_for_review(
                pipeline_results["recommendations"],
                f"{output_path}/recommendations"
            )
        
        # Save summary as JSON
        import json
        summary_data = {
            k: v for k, v in pipeline_results.items() 
            if k not in ["recommendations"]  # Exclude DataFrames
        }
        
        with open(f"{output_path}/pipeline_summary.json", "w") as f:
            json.dump(summary_data, f, indent=2, default=str)
        
        logger.info(f"Results saved to {output_path}")


def main():
    """Main entry point for the pipeline."""
    
    # Initialize pipeline
    pipeline = GeneticTestingPipeline()
    
    # Run pipeline
    results = pipeline.run_full_pipeline()
    
    # Print summary
    print("\n" + "="*50)
    print("GENETIC TESTING PIPELINE RESULTS")
    print("="*50)
    print(f"Status: {results.get('pipeline_status', 'unknown')}")
    print(f"Execution Time: {results.get('execution_time_seconds', 0):.2f} seconds")
    print(f"Genetic Patients Identified: {results.get('genetic_patients_identified', 0)}")
    print(f"Similar Patients Found: {results.get('similar_patients_found', 0)}")
    print(f"Recommendations Generated: {results.get('recommendations_generated', 0)}")
    
    if results.get("pipeline_status") == "failed":
        print(f"Error: {results.get('error_message', 'Unknown error')}")
        sys.exit(1)
    
    # Save results
    pipeline.save_results(results, "/tmp/genetic_testing_results")
    
    print("\nPipeline completed successfully!")


if __name__ == "__main__":
    main()
