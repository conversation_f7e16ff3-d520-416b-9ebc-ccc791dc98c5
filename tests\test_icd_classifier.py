"""
Unit tests for ICD-10 genetic disorder classifier.
"""

import pytest
import pandas as pd
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, DateType, ArrayType

from src.models.icd_classifier import ICD10GeneticClassifier


@pytest.fixture(scope="session")
def spark():
    """Create Spark session for testing."""
    return SparkSession.builder \
        .appName("GeneticTestingTests") \
        .master("local[2]") \
        .config("spark.sql.adaptive.enabled", "false") \
        .getOrCreate()


@pytest.fixture
def icd_classifier(spark):
    """Create ICD classifier instance."""
    return ICD10GeneticClassifier(spark)


@pytest.fixture
def sample_medical_claims(spark):
    """Create sample medical claims data for testing."""
    
    schema = StructType([
        StructField("claim_id", StringType(), False),
        <PERSON><PERSON>ct<PERSON><PERSON>("patient_id", StringType(), False),
        <PERSON><PERSON><PERSON><PERSON><PERSON>("service_date", DateType(), False),
        <PERSON><PERSON>ct<PERSON><PERSON>("icd10_primary", StringType(), True),
        Struct<PERSON><PERSON>("icd10_secondary", ArrayType(StringType()), True),
    ])
    
    data = [
        ("C001", "P001", "2023-01-15", "Z80.0", ["Z80.1"]),  # Family history cancer
        ("C002", "P002", "2023-01-16", "Q90.0", []),         # Down syndrome
        ("C003", "P003", "2023-01-17", "E70.0", ["E71.0"]),  # Metabolic disorder
        ("C004", "P004", "2023-01-18", "I10", []),           # Hypertension (not genetic)
        ("C005", "P005", "2023-01-19", "Z82.3", []),         # Family history diabetes
        ("C006", "P001", "2023-01-20", "Z80.2", []),         # Same patient, different genetic code
    ]
    
    return spark.createDataFrame(data, schema)


class TestICD10GeneticClassifier:
    """Test cases for ICD-10 genetic classifier."""
    
    def test_classify_single_icd_code(self, icd_classifier):
        """Test classification of individual ICD codes."""
        
        # Test genetic codes
        assert "family_history_malignant" in icd_classifier.classify_icd_code("Z80.0")
        assert "chromosomal_abnormalities" in icd_classifier.classify_icd_code("Q90.0")
        assert "metabolic_disorders" in icd_classifier.classify_icd_code("E70.0")
        
        # Test non-genetic codes
        assert icd_classifier.classify_icd_code("I10") == []
        assert icd_classifier.classify_icd_code("J44.0") == []
        
        # Test invalid codes
        assert icd_classifier.classify_icd_code("") == []
        assert icd_classifier.classify_icd_code(None) == []
    
    def test_identify_genetic_patients(self, icd_classifier, sample_medical_claims):
        """Test identification of genetic patients from claims."""
        
        genetic_patients = icd_classifier.identify_genetic_patients(
            sample_medical_claims,
            min_genetic_claims=1
        )
        
        # Should identify 3 patients with genetic diagnoses
        patient_ids = [row.patient_id for row in genetic_patients.collect()]
        assert "P001" in patient_ids  # Family history codes
        assert "P002" in patient_ids  # Chromosomal abnormality
        assert "P003" in patient_ids  # Metabolic disorder
        assert "P005" in patient_ids  # Family history diabetes
        assert "P004" not in patient_ids  # No genetic codes
        
        # Check claim counts
        p001_data = genetic_patients.filter(genetic_patients.patient_id == "P001").collect()[0]
        assert p001_data.total_genetic_claims == 2  # Two genetic claims for P001
    
    def test_genetic_risk_indicators(self, icd_classifier, sample_medical_claims):
        """Test genetic risk indicator calculation."""
        
        risk_indicators = icd_classifier.get_genetic_risk_indicators(
            sample_medical_claims, "P001"
        )
        
        assert risk_indicators["patient_id"] == "P001"
        assert "family_history_malignant" in risk_indicators["genetic_categories"]
        assert len(risk_indicators["family_history_indicators"]) > 0
        assert risk_indicators["overall_genetic_risk_score"] > 0
    
    def test_validation_with_test_cases(self, icd_classifier):
        """Test classifier validation with known test cases."""
        
        test_cases = [
            ("Z80.0", ["family_history_malignant"]),
            ("Q90.0", ["chromosomal_abnormalities"]),
            ("E70.0", ["metabolic_disorders"]),
            ("I10", []),  # Non-genetic
            ("Z82.3", ["family_history_chronic_diseases"]),
        ]
        
        validation_results = icd_classifier.validate_genetic_classification(test_cases)
        
        assert validation_results["overall_accuracy"] > 0.8  # Should be high accuracy
        assert validation_results["total_test_cases"] == len(test_cases)
    
    def test_genetic_code_mapping_generation(self, icd_classifier):
        """Test generation of genetic code mapping."""
        
        mapping_df = icd_classifier.generate_genetic_code_mapping()
        
        # Should have mappings for all genetic categories
        mapping_count = mapping_df.count()
        assert mapping_count > 0
        
        # Check that family history patterns are included
        family_history_mappings = mapping_df.filter(
            mapping_df.genetic_category.contains("family_history")
        ).count()
        assert family_history_mappings > 0
    
    def test_edge_cases(self, icd_classifier):
        """Test edge cases and error handling."""
        
        # Test with malformed ICD codes
        assert icd_classifier.classify_icd_code("INVALID") == []
        assert icd_classifier.classify_icd_code("123") == []
        
        # Test with partial codes
        assert len(icd_classifier.classify_icd_code("Z80")) == 0  # Needs full code
        
        # Test case sensitivity
        assert icd_classifier.classify_icd_code("z80.0") == icd_classifier.classify_icd_code("Z80.0")


@pytest.fixture
def sample_patient_features(spark):
    """Create sample patient features for testing."""
    
    data = [
        ("P001", 15, 8, 5000.0, 1200.0, 3, 2, 0.8, 0.6),
        ("P002", 22, 12, 8000.0, 2000.0, 5, 3, 0.9, 0.7),
        ("P003", 8, 4, 2000.0, 500.0, 2, 1, 0.5, 0.4),
    ]
    
    columns = [
        "patient_id", "medical_claim_count", "pharmacy_claim_count",
        "total_medical_cost", "total_pharmacy_cost", "unique_providers",
        "unique_specialties", "specialist_visit_ratio", "chronic_medication_ratio"
    ]
    
    return spark.createDataFrame(data, columns)


class TestIntegration:
    """Integration tests for the classifier with other components."""
    
    def test_end_to_end_classification(self, icd_classifier, sample_medical_claims, sample_patient_features):
        """Test end-to-end classification workflow."""
        
        # Identify genetic patients
        genetic_patients = icd_classifier.identify_genetic_patients(sample_medical_claims)
        
        # Should have identified patients
        assert genetic_patients.count() > 0
        
        # Join with patient features
        genetic_with_features = genetic_patients.join(
            sample_patient_features, "patient_id", "inner"
        )
        
        # Should maintain data integrity
        assert genetic_with_features.count() > 0
        
        # Check that all genetic patients have features
        genetic_patient_ids = {row.patient_id for row in genetic_patients.collect()}
        feature_patient_ids = {row.patient_id for row in sample_patient_features.collect()}
        
        # At least some overlap should exist
        overlap = genetic_patient_ids.intersection(feature_patient_ids)
        assert len(overlap) > 0


if __name__ == "__main__":
    pytest.main([__file__])
