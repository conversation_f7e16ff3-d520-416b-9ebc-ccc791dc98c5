"""
HealthVerity-specific data loader for genetic testing ML pipeline.
Adapted for the HealthVerity claims sample dataset structure.
"""

import logging
from typing import Optional, List, Dict, Any
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import col, when, isnan, isnull, count, regexp_replace, upper, trim
from databricks.sql import connect

from ..utils.config import get_config

logger = logging.getLogger(__name__)


class HealthVerityDataLoader:
    """Data loader specifically for HealthVerity claims sample dataset."""
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = get_config()
        
        # HealthVerity specific configuration
        self.catalog_name = "healthverity_claims_sample_patient_dataset"
        self.database_name = "hv_claims_sample"
        
        # Column mappings - Updated for HealthVerity actual schema
        self.column_mappings = {
            'patient_id': 'patient_id',
            'icd_code': 'diagnosis_code',    # Actual HealthVerity column name
            'diagnosis_date': 'date_service',  # Actual HealthVerity column name
            'claim_id': 'claim_id',
        }
        
    def load_diagnosis_data(self, 
                           patient_ids: Optional[List[str]] = None,
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None,
                           icd_codes: Optional[List[str]] = None,
                           limit: Optional[int] = None) -> DataFrame:
        """
        Load diagnosis data from HealthVerity table.
        
        Args:
            patient_ids: List of patient IDs to filter
            start_date: Start date for diagnosis filter (YYYY-MM-DD)
            end_date: End date for diagnosis filter (YYYY-MM-DD)
            icd_codes: List of ICD codes to filter
            limit: Maximum number of records to return
            
        Returns:
            Spark DataFrame with diagnosis data
        """
        table_name = f"{self.catalog_name}.{self.database_name}.diagnosis"
        
        logger.info(f"Loading diagnosis data from {table_name}")
        
        try:
            df = self.spark.table(table_name)
        except Exception as e:
            logger.error(f"Failed to load table {table_name}: {str(e)}")
            # Try alternative SQL approach
            df = self.spark.sql(f"SELECT * FROM {table_name}")
        
        # Apply filters
        if patient_ids:
            patient_col = self._get_column_name('patient_id', df.columns)
            if patient_col:
                df = df.filter(col(patient_col).isin(patient_ids))
        
        if start_date and self._get_column_name('diagnosis_date', df.columns):
            date_col = self._get_column_name('diagnosis_date', df.columns)
            df = df.filter(col(date_col) >= start_date)
        
        if end_date and self._get_column_name('diagnosis_date', df.columns):
            date_col = self._get_column_name('diagnosis_date', df.columns)
            df = df.filter(col(date_col) <= end_date)
        
        if icd_codes:
            icd_col = self._get_column_name('icd_code', df.columns)
            if icd_col:
                df = df.filter(col(icd_col).isin(icd_codes))
        
        if limit:
            df = df.limit(limit)
        
        # Standardize column names
        df = self._standardize_columns(df)
        
        logger.info(f"Loaded {df.count()} diagnosis records")
        
        return df
    
    def _get_column_name(self, standard_name: str, available_columns: List[str]) -> Optional[str]:
        """
        Find the actual column name that matches our standard naming.
        
        Args:
            standard_name: Standard column name we're looking for
            available_columns: List of available column names in the table
            
        Returns:
            Actual column name or None if not found
        """
        # Direct mapping first
        if standard_name in self.column_mappings:
            mapped_name = self.column_mappings[standard_name]
            if mapped_name in available_columns:
                return mapped_name
        
        # Fuzzy matching for common variations
        variations = {
            'patient_id': ['patient_id', 'patientid', 'patient_key', 'patient_number', 'ptid'],
            'icd_code': ['diagnosis_code', 'icd_code', 'icd10_code', 'icd', 'primary_diagnosis', 'dx_code'],
            'diagnosis_date': ['date_service', 'diagnosis_date', 'service_date', 'date_of_service', 'claim_date', 'dx_date'],
            'claim_id': ['claim_id', 'claimid', 'claim_number', 'claim_key']
        }
        
        if standard_name in variations:
            for variant in variations[standard_name]:
                # Case-insensitive search
                for col_name in available_columns:
                    if variant.lower() == col_name.lower():
                        return col_name
        
        return None
    
    def _standardize_columns(self, df: DataFrame) -> DataFrame:
        """
        Standardize column names and clean data.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with standardized columns
        """
        # Create mapping of actual columns to standard names
        column_renames = {}
        
        for standard_name in ['patient_id', 'icd_code', 'diagnosis_date', 'claim_id']:
            actual_name = self._get_column_name(standard_name, df.columns)
            if actual_name and actual_name != standard_name:
                column_renames[actual_name] = standard_name
        
        # Rename columns
        for old_name, new_name in column_renames.items():
            df = df.withColumnRenamed(old_name, new_name)
        
        # Clean ICD codes if present
        if 'icd_code' in df.columns:
            df = df.withColumn('icd_code', 
                              upper(trim(regexp_replace(col('icd_code'), '[^A-Z0-9.]', ''))))
        
        return df
    
    def explore_table_structure(self, table_name: str = "diagnosis") -> Dict[str, Any]:
        """
        Explore the structure of a HealthVerity table.
        
        Args:
            table_name: Name of the table to explore
            
        Returns:
            Dictionary with table structure information
        """
        full_table_name = f"{self.catalog_name}.{self.database_name}.{table_name}"
        
        try:
            df = self.spark.table(full_table_name)
        except Exception as e:
            logger.error(f"Failed to load table {full_table_name}: {str(e)}")
            return {"error": str(e)}
        
        # Get basic info
        total_rows = df.count()
        columns = df.columns
        
        # Get schema
        schema_info = []
        for field in df.schema.fields:
            schema_info.append({
                "name": field.name,
                "type": str(field.dataType),
                "nullable": field.nullable
            })
        
        # Sample data
        sample_data = df.limit(5).collect()
        
        # Look for potential ICD codes
        potential_icd_columns = []
        for col_name in columns:
            if any(keyword in col_name.lower() for keyword in ['icd', 'diagnosis', 'code', 'dx']):
                potential_icd_columns.append(col_name)
        
        # Check for genetic disorder patterns in potential ICD columns
        genetic_patterns_found = {}
        for col_name in potential_icd_columns:
            try:
                genetic_count = df.filter(
                    col(col_name).rlike("^(Z8[0-4]|Q9[0-9]|E7[0-9])")
                ).count()
                genetic_patterns_found[col_name] = genetic_count
            except:
                genetic_patterns_found[col_name] = 0
        
        return {
            "table_name": full_table_name,
            "total_rows": total_rows,
            "total_columns": len(columns),
            "columns": columns,
            "schema": schema_info,
            "sample_data": [row.asDict() for row in sample_data],
            "potential_icd_columns": potential_icd_columns,
            "genetic_patterns_found": genetic_patterns_found
        }
    
    def identify_genetic_patients_healthverity(self, 
                                              lookback_days: int = 365,
                                              min_genetic_claims: int = 1) -> DataFrame:
        """
        Identify patients with genetic disorder diagnoses from HealthVerity data.
        
        Args:
            lookback_days: Number of days to look back for diagnoses
            min_genetic_claims: Minimum number of genetic claims required
            
        Returns:
            DataFrame with genetic patients
        """
        logger.info("Identifying genetic patients from HealthVerity data")
        
        # Load diagnosis data
        diagnosis_df = self.load_diagnosis_data()
        
        # Define genetic disorder patterns
        genetic_patterns = [
            "Z8[0-4].*",  # Family history codes Z80-Z84
            "Q9[0-9].*",  # Chromosomal abnormalities Q90-Q99
            "E7[0-9].*",  # Metabolic disorders E70-E79
            "Z15.*",      # Genetic susceptibility to malignant neoplasm
            "Z14.*",      # Genetic carrier status
        ]
        
        # Find the ICD code column
        icd_col = self._get_column_name('icd_code', diagnosis_df.columns)
        if not icd_col:
            raise ValueError("Could not find ICD code column in diagnosis data")
        
        # Filter for genetic diagnoses
        genetic_filter = "|".join([f"^{pattern}" for pattern in genetic_patterns])
        
        genetic_diagnoses = diagnosis_df.filter(
            col(icd_col).rlike(genetic_filter)
        )
        
        # Get patient ID column
        patient_col = self._get_column_name('patient_id', diagnosis_df.columns)
        if not patient_col:
            raise ValueError("Could not find patient ID column in diagnosis data")
        
        # Aggregate by patient
        genetic_patients = (
            genetic_diagnoses
            .groupBy(patient_col)
            .agg(
                count("*").alias("genetic_claim_count"),
                collect_set(icd_col).alias("genetic_icd_codes"),
                min("diagnosis_date").alias("first_genetic_diagnosis") if 'diagnosis_date' in diagnosis_df.columns else lit(None).alias("first_genetic_diagnosis"),
                max("diagnosis_date").alias("last_genetic_diagnosis") if 'diagnosis_date' in diagnosis_df.columns else lit(None).alias("last_genetic_diagnosis")
            )
            .filter(col("genetic_claim_count") >= min_genetic_claims)
        )
        
        # Standardize column names
        if patient_col != 'patient_id':
            genetic_patients = genetic_patients.withColumnRenamed(patient_col, 'patient_id')
        
        logger.info(f"Identified {genetic_patients.count()} patients with genetic diagnoses")
        
        return genetic_patients
    
    def get_data_quality_report(self, table_name: str = "diagnosis") -> Dict[str, Any]:
        """Generate data quality report for HealthVerity table."""
        
        full_table_name = f"{self.catalog_name}.{self.database_name}.{table_name}"
        
        try:
            df = self.spark.table(full_table_name)
        except Exception as e:
            return {"error": f"Failed to load table: {str(e)}"}
        
        total_rows = df.count()
        
        # Calculate null percentages for each column
        null_stats = {}
        for col_name in df.columns:
            null_count = df.filter(col(col_name).isNull()).count()
            null_percentage = (null_count / total_rows) * 100 if total_rows > 0 else 0
            null_stats[col_name] = {
                "null_count": null_count,
                "null_percentage": null_percentage
            }
        
        # Get date range if date column exists
        date_range = {}
        date_col = self._get_column_name('diagnosis_date', df.columns)
        if date_col:
            date_stats = df.select(
                min(col(date_col)).alias("min_date"),
                max(col(date_col)).alias("max_date")
            ).collect()[0]
            date_range = {
                "min_date": str(date_stats.min_date),
                "max_date": str(date_stats.max_date)
            }
        
        return {
            "table_name": full_table_name,
            "total_rows": total_rows,
            "total_columns": len(df.columns),
            "null_statistics": null_stats,
            "date_range": date_range
        }
    
    def update_column_mappings(self, mappings: Dict[str, str]):
        """
        Update column mappings based on actual table structure.
        
        Args:
            mappings: Dictionary of standard_name -> actual_column_name
        """
        self.column_mappings.update(mappings)
        logger.info(f"Updated column mappings: {self.column_mappings}")


def create_healthverity_loader(spark: SparkSession) -> HealthVerityDataLoader:
    """Factory function to create HealthVerity data loader."""
    return HealthVerityDataLoader(spark)
