# Model Configuration for Genetic Testing ML Pipeline

# Patient Profiling Configuration
patient_profiling:
  # Feature categories to include
  profile_features:
    demographic: true
    claim_frequency: true
    cost_patterns: true
    provider_patterns: true
    temporal_patterns: true
    drug_interactions: true
    comorbidity_patterns: true
  
  # Clustering for patient segmentation
  clustering:
    algorithm: "kmeans"  # Options: kmeans, dbscan, hierarchical
    n_clusters: 10
    random_state: 42
    max_iter: 300
    
  # Dimensionality reduction
  dimensionality_reduction:
    method: "pca"  # Options: pca, umap, tsne
    n_components: 50
    
# Similarity Matching Configuration
similarity_matching:
  # Similarity computation method
  method: "cosine"  # Options: cosine, euclidean, jaccard, hamming
  
  # Embedding configuration
  embeddings:
    dimension: 128
    use_pretrained: true
    model_name: "sentence-transformers/all-MiniLM-L6-v2"
    
  # Similarity thresholds
  thresholds:
    high_similarity: 0.8
    moderate_similarity: 0.6
    low_similarity: 0.4
    
  # Nearest neighbors
  n_neighbors: 10
  algorithm: "auto"  # Options: auto, ball_tree, kd_tree, brute

# Risk Scoring Model Configuration
risk_scoring:
  # Primary model type
  model_type: "xgboost"  # Options: xgboost, lightgbm, random_forest, logistic_regression
  
  # Model parameters
  xgboost:
    n_estimators: 100
    max_depth: 6
    learning_rate: 0.1
    subsample: 0.8
    colsample_bytree: 0.8
    random_state: 42
    
  lightgbm:
    n_estimators: 100
    max_depth: 6
    learning_rate: 0.1
    subsample: 0.8
    colsample_bytree: 0.8
    random_state: 42
    
  random_forest:
    n_estimators: 100
    max_depth: 10
    min_samples_split: 5
    min_samples_leaf: 2
    random_state: 42
    
  # Cross-validation
  cross_validation:
    folds: 5
    stratified: true
    shuffle: true
    random_state: 42
    
  # Hyperparameter tuning
  hyperparameter_tuning:
    enabled: true
    method: "optuna"  # Options: optuna, grid_search, random_search
    n_trials: 100
    timeout: 3600  # seconds
    
# Feature Engineering Configuration
feature_engineering:
  # Feature selection
  feature_selection:
    method: "mutual_info"  # Options: mutual_info, chi2, f_classif, rfe
    max_features: 100
    
  # Feature scaling
  scaling:
    method: "standard"  # Options: standard, minmax, robust, quantile
    
  # Feature creation
  create_interaction_features: true
  create_polynomial_features: false
  polynomial_degree: 2
  
  # Temporal features
  temporal_features:
    rolling_windows: [7, 30, 90, 180, 365]  # days
    lag_features: [1, 7, 30, 90]  # days
    seasonal_features: true
    
# ICD-10 Classification Configuration
icd10_classification:
  # Genetic disorder code patterns
  genetic_code_patterns:
    family_history: ["Z80", "Z81", "Z82", "Z83", "Z84"]
    chromosomal_abnormalities: ["Q90", "Q91", "Q93", "Q95", "Q96", "Q97", "Q98", "Q99"]
    metabolic_disorders: ["E70", "E71", "E72", "E74", "E75", "E76", "E77", "E78", "E79", "E80"]
    
  # Classification rules
  classification_rules:
    require_primary_diagnosis: false
    include_secondary_diagnoses: true
    min_diagnosis_count: 1
    
# Model Evaluation Configuration
evaluation:
  # Metrics to compute
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "roc_auc"
    - "pr_auc"
    
  # Evaluation strategy
  test_size: 0.2
  validation_size: 0.2
  stratify: true
  
  # Threshold optimization
  threshold_optimization:
    enabled: true
    metric: "f1_score"  # Metric to optimize
    
# Recommendation Engine Configuration
recommendation_engine:
  # Risk level thresholds
  risk_thresholds:
    very_high: 0.8
    high: 0.6
    moderate: 0.4
    low: 0.0
    
  # Recommendation criteria
  criteria:
    min_confidence: 0.5
    max_recommendations_per_batch: 1000
    
  # Genetic test recommendations
  genetic_tests:
    - name: "Comprehensive Cancer Panel"
      conditions: ["family_history_cancer", "multiple_cancers"]
      cost: 3000
      
    - name: "Cardiac Genetic Panel"
      conditions: ["cardiomyopathy", "arrhythmia", "family_history_cardiac"]
      cost: 2500
      
    - name: "Metabolic Disorder Panel"
      conditions: ["metabolic_disorders", "developmental_delays"]
      cost: 2000
      
    - name: "Pharmacogenomic Testing"
      conditions: ["adverse_drug_reactions", "medication_ineffectiveness"]
      cost: 500

# Data Quality Configuration
data_quality:
  # Validation rules
  validation_rules:
    patient_id_format: "^[A-Z0-9]{8,12}$"
    date_range_years: 10
    max_claim_amount: 100000
    
  # Outlier detection
  outlier_detection:
    enabled: true
    method: "isolation_forest"  # Options: isolation_forest, local_outlier_factor
    contamination: 0.1
    
# Monitoring and Alerting
monitoring:
  # Model performance monitoring
  performance_monitoring:
    enabled: true
    check_interval_hours: 24
    performance_threshold: 0.8
    
  # Data drift detection
  data_drift:
    enabled: true
    reference_window_days: 30
    detection_window_days: 7
    drift_threshold: 0.1
    
  # Alerts
  alerts:
    email_recipients: ["<EMAIL>"]
    slack_webhook: "${SLACK_WEBHOOK_URL}"
