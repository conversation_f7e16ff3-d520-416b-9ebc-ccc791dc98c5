# Databricks notebook source
# MAGIC %md
# MAGIC # HealthVerity Diagnosis Data Exploration for Genetic Testing
# MAGIC 
# MAGIC This notebook explores the HealthVerity diagnosis table to understand the data structure and identify genetic disorder patterns.
# MAGIC 
# MAGIC **Data Source:** `healthverity_claims_sample_patient_dataset.hv_claims_sample.diagnosis`

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup and Configuration

# COMMAND ----------

import sys
import os

# Add the genetic testing source code to path
sys.path.append('/Workspace/genetic_testing/src')

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import pandas as pd

# Import our genetic testing modules
from models.icd_classifier import ICD10GeneticClassifier
from utils.config import get_config

# Initialize configuration
config = get_config()

print("HealthVerity Databricks Environment:")
print(f"Host: {config.settings.databricks_host}")
print(f"Catalog: {config.settings.catalog_name}")
print(f"Database: {config.settings.database_name}")
print(f"Diagnosis Table: {config.get_table_name('diagnosis')}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Explore Diagnosis Table Structure

# COMMAND ----------

# Load the diagnosis table
diagnosis_table = config.get_table_name('diagnosis')
print(f"Loading table: {diagnosis_table}")

try:
    diagnosis_df = spark.table(diagnosis_table)
    print(f"✅ Successfully loaded diagnosis table")
    print(f"Total records: {diagnosis_df.count():,}")
except Exception as e:
    print(f"❌ Error loading table: {str(e)}")
    # Try alternative access method
    try:
        diagnosis_df = spark.sql(f"SELECT * FROM {diagnosis_table}")
        print(f"✅ Successfully loaded via SQL")
        print(f"Total records: {diagnosis_df.count():,}")
    except Exception as e2:
        print(f"❌ SQL access also failed: {str(e2)}")

# COMMAND ----------

# Examine table schema
print("=== DIAGNOSIS TABLE SCHEMA ===")
diagnosis_df.printSchema()

print("\n=== COLUMN NAMES ===")
for i, col_name in enumerate(diagnosis_df.columns):
    print(f"{i+1:2d}. {col_name}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Sample Data Examination

# COMMAND ----------

# Show sample records
print("=== SAMPLE RECORDS ===")
diagnosis_df.limit(10).show(truncate=False)

# Get basic statistics
print("\n=== BASIC STATISTICS ===")
print(f"Total rows: {diagnosis_df.count():,}")
print(f"Total columns: {len(diagnosis_df.columns)}")

# Check for key columns we need
required_columns = ['patient_id', 'icd_code', 'diagnosis_date', 'icd10_code', 'diagnosis_code']
available_columns = diagnosis_df.columns

print(f"\n=== COLUMN AVAILABILITY CHECK ===")
for col in required_columns:
    if col in available_columns:
        print(f"✅ {col} - Available")
    else:
        print(f"❌ {col} - Missing")

# Show actual column names for mapping
print(f"\n=== ACTUAL COLUMNS ===")
for col in available_columns:
    print(f"  {col}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Identify ICD Code Columns

# COMMAND ----------

# Look for ICD code patterns in the data
print("=== IDENTIFYING ICD CODE COLUMNS ===")

# Check each column for ICD-like patterns
for col_name in diagnosis_df.columns:
    if any(keyword in col_name.lower() for keyword in ['icd', 'diagnosis', 'code']):
        print(f"\n--- Column: {col_name} ---")
        
        # Get sample values
        sample_values = diagnosis_df.select(col_name).filter(col(col_name).isNotNull()).limit(10).collect()
        print("Sample values:")
        for i, row in enumerate(sample_values[:5]):
            print(f"  {i+1}. {row[0]}")
        
        # Check for ICD-10 pattern (letter + numbers)
        icd10_pattern_count = diagnosis_df.filter(
            col(col_name).rlike("^[A-Z][0-9]{2}.*")
        ).count()
        
        print(f"Records matching ICD-10 pattern: {icd10_pattern_count:,}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Genetic Disorder Analysis

# COMMAND ----------

# Initialize genetic classifier
icd_classifier = ICD10GeneticClassifier(spark)

# Let's assume the main ICD column is identified (update based on your findings above)
# Common column names: 'icd_code', 'icd10_code', 'diagnosis_code', 'primary_diagnosis'

# You'll need to update this based on your actual column name
ICD_COLUMN = 'icd_code'  # Update this based on exploration above

if ICD_COLUMN in diagnosis_df.columns:
    print(f"=== GENETIC DISORDER ANALYSIS USING {ICD_COLUMN} ===")
    
    # Get unique ICD codes
    unique_codes = diagnosis_df.select(ICD_COLUMN).distinct().count()
    print(f"Unique ICD codes: {unique_codes:,}")
    
    # Look for genetic disorder patterns
    genetic_patterns = [
        "Z8[0-4].*",  # Family history codes
        "Q9[0-9].*",  # Chromosomal abnormalities
        "E7[0-9].*"   # Metabolic disorders
    ]
    
    for pattern in genetic_patterns:
        pattern_count = diagnosis_df.filter(col(ICD_COLUMN).rlike(pattern)).count()
        print(f"Pattern {pattern}: {pattern_count:,} records")
    
    # Get top ICD codes
    print(f"\n=== TOP 20 ICD CODES ===")
    top_codes = (
        diagnosis_df
        .groupBy(ICD_COLUMN)
        .count()
        .orderBy(desc("count"))
        .limit(20)
    )
    top_codes.show()
    
    # Look specifically for genetic codes
    print(f"\n=== GENETIC DISORDER CODES FOUND ===")
    genetic_codes = (
        diagnosis_df
        .filter(col(ICD_COLUMN).rlike("^(Z8[0-4]|Q9[0-9]|E7[0-9])"))
        .groupBy(ICD_COLUMN)
        .count()
        .orderBy(desc("count"))
    )
    
    genetic_count = genetic_codes.count()
    print(f"Found {genetic_count} different genetic disorder codes")
    
    if genetic_count > 0:
        genetic_codes.show(50)
    else:
        print("No genetic disorder codes found with standard patterns")
        
        # Try broader search
        print("\n=== BROADER GENETIC CODE SEARCH ===")
        broader_genetic = (
            diagnosis_df
            .filter(col(ICD_COLUMN).rlike("^(Z|Q|E)"))
            .groupBy(ICD_COLUMN)
            .count()
            .orderBy(desc("count"))
            .limit(20)
        )
        broader_genetic.show()

else:
    print(f"❌ Column {ICD_COLUMN} not found. Please update the column name based on exploration above.")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Patient Analysis

# COMMAND ----------

# Analyze patient patterns
PATIENT_ID_COLUMN = 'patient_id'  # Update if different

if PATIENT_ID_COLUMN in diagnosis_df.columns:
    print(f"=== PATIENT ANALYSIS USING {PATIENT_ID_COLUMN} ===")
    
    # Patient statistics
    total_patients = diagnosis_df.select(PATIENT_ID_COLUMN).distinct().count()
    total_diagnoses = diagnosis_df.count()
    
    print(f"Total unique patients: {total_patients:,}")
    print(f"Total diagnoses: {total_diagnoses:,}")
    print(f"Average diagnoses per patient: {total_diagnoses/total_patients:.2f}")
    
    # Diagnoses per patient distribution
    diagnoses_per_patient = (
        diagnosis_df
        .groupBy(PATIENT_ID_COLUMN)
        .count()
        .select("count")
        .describe()
    )
    
    print(f"\n=== DIAGNOSES PER PATIENT STATISTICS ===")
    diagnoses_per_patient.show()
    
    # If we found genetic codes, analyze genetic patients
    if ICD_COLUMN in diagnosis_df.columns:
        genetic_patients = (
            diagnosis_df
            .filter(col(ICD_COLUMN).rlike("^(Z8[0-4]|Q9[0-9]|E7[0-9])"))
            .select(PATIENT_ID_COLUMN)
            .distinct()
        )
        
        genetic_patient_count = genetic_patients.count()
        genetic_prevalence = (genetic_patient_count / total_patients) * 100
        
        print(f"\n=== GENETIC DISORDER PATIENTS ===")
        print(f"Patients with genetic diagnoses: {genetic_patient_count:,}")
        print(f"Genetic diagnosis prevalence: {genetic_prevalence:.2f}%")

else:
    print(f"❌ Patient ID column not found. Available columns: {diagnosis_df.columns}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Date Analysis

# COMMAND ----------

# Look for date columns
date_columns = [col for col in diagnosis_df.columns if 'date' in col.lower()]

print(f"=== DATE COLUMNS FOUND ===")
for col_name in date_columns:
    print(f"  {col_name}")

if date_columns:
    # Analyze the first date column
    date_col = date_columns[0]
    print(f"\n=== ANALYZING {date_col} ===")
    
    # Get date range
    date_stats = diagnosis_df.select(
        min(col(date_col)).alias("min_date"),
        max(col(date_col)).alias("max_date"),
        count(col(date_col)).alias("non_null_dates")
    ).collect()[0]
    
    print(f"Date range: {date_stats.min_date} to {date_stats.max_date}")
    print(f"Non-null dates: {date_stats.non_null_dates:,}")
    
    # Monthly distribution
    monthly_dist = (
        diagnosis_df
        .withColumn("year_month", date_format(col(date_col), "yyyy-MM"))
        .groupBy("year_month")
        .count()
        .orderBy("year_month")
    )
    
    print(f"\n=== MONTHLY DIAGNOSIS DISTRIBUTION ===")
    monthly_dist.show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Data Quality Assessment

# COMMAND ----------

print("=== DATA QUALITY ASSESSMENT ===")

# Check for null values in key columns
key_columns = [col for col in diagnosis_df.columns if any(keyword in col.lower() 
               for keyword in ['patient', 'icd', 'diagnosis', 'date', 'code'])]

print(f"Checking data quality for key columns: {key_columns}")

total_rows = diagnosis_df.count()

for col_name in key_columns:
    null_count = diagnosis_df.filter(col(col_name).isNull()).count()
    null_percentage = (null_count / total_rows) * 100
    
    print(f"{col_name:20s}: {null_count:8,} nulls ({null_percentage:5.2f}%)")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Recommendations for Genetic Testing Pipeline

# COMMAND ----------

print("=== RECOMMENDATIONS FOR GENETIC TESTING PIPELINE ===")

print("\n1. DATA MAPPING:")
print("   Based on the exploration above, update the following mappings:")
print("   - Patient ID column: [UPDATE BASED ON FINDINGS]")
print("   - ICD code column: [UPDATE BASED ON FINDINGS]") 
print("   - Date column: [UPDATE BASED ON FINDINGS]")

print("\n2. GENETIC CODE AVAILABILITY:")
if 'genetic_count' in locals() and genetic_count > 0:
    print(f"   ✅ Found {genetic_count} genetic disorder codes")
    print("   ✅ Ready to proceed with genetic patient identification")
else:
    print("   ⚠️  Limited genetic codes found - may need broader search patterns")
    print("   ⚠️  Consider expanding genetic code definitions")

print("\n3. DATA VOLUME:")
print(f"   - Total diagnoses: {diagnosis_df.count():,}")
if 'total_patients' in locals():
    print(f"   - Total patients: {total_patients:,}")
    if total_patients > 10000:
        print("   ✅ Sufficient volume for ML model training")
    else:
        print("   ⚠️  May need more data for robust ML models")

print("\n4. NEXT STEPS:")
print("   1. Update column mappings in the genetic testing pipeline")
print("   2. Create data loader specifically for HealthVerity format")
print("   3. Run genetic patient identification")
print("   4. Proceed with ML model development")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Export Configuration

# COMMAND ----------

# Generate configuration for the genetic testing pipeline
print("=== CONFIGURATION FOR GENETIC TESTING PIPELINE ===")

config_template = f"""
# HealthVerity Databricks Configuration
DATABRICKS_HOST=adb-3961822481655844.4.azuredatabricks.net
DATABRICKS_HTTP_PATH=/sql/1.0/warehouses/3b64ec91bab74a2f
CATALOG_NAME=healthverity_claims_sample_patient_dataset
DATABASE_NAME=hv_claims_sample
DIAGNOSIS_TABLE=diagnosis

# Column Mappings (UPDATE BASED ON EXPLORATION)
PATIENT_ID_COLUMN=patient_id  # Update if different
ICD_CODE_COLUMN=icd_code      # Update if different  
DATE_COLUMN=diagnosis_date    # Update if different

# Processing Parameters
LOOKBACK_DAYS=365
MIN_CLAIMS_THRESHOLD=3
SIMILARITY_THRESHOLD=0.7
RISK_THRESHOLD=0.5
"""

print(config_template)

print("\n=== SAVE THIS CONFIGURATION ===")
print("Save the above configuration to a .env file or update the config files")
print("in the genetic testing pipeline with the correct column mappings.")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Summary
# MAGIC 
# MAGIC This exploration notebook has:
# MAGIC 1. ✅ Connected to your HealthVerity Databricks environment
# MAGIC 2. ✅ Explored the diagnosis table structure
# MAGIC 3. ✅ Identified available columns and data patterns
# MAGIC 4. ✅ Searched for genetic disorder codes
# MAGIC 5. ✅ Assessed data quality and volume
# MAGIC 6. ✅ Provided recommendations for pipeline adaptation
# MAGIC 
# MAGIC **Next Steps:**
# MAGIC 1. Update column mappings based on findings above
# MAGIC 2. Adapt the genetic testing pipeline for HealthVerity data format
# MAGIC 3. Run the genetic patient identification process
# MAGIC 4. Develop ML models for similarity matching and risk scoring
