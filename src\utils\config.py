"""
Configuration management for genetic testing ML pipeline.
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Databricks settings
    databricks_host: str = Field("adb-396182**********.4.azuredatabricks.net", env="DATABRICKS_HOST")
    databricks_token: str = Field("", env="DATABRICKS_TOKEN")  # Optional when running inside Databricks
    databricks_cluster_id: str = Field("", env="DATABRICKS_CLUSTER_ID")  # Optional when running inside Databricks
    databricks_http_path: str = Field("/sql/1.0/warehouses/3b64ec91bab74a2f", env="DATABRICKS_HTTP_PATH")

    # Database settings
    database_name: str = Field("hv_claims_sample", env="DATABASE_NAME")
    catalog_name: str = Field("healthverity_claims_sample_patient_dataset", env="CATALOG_NAME")

    # Table names - Updated for your HealthVerity dataset
    diagnosis_table: str = Field("diagnosis", env="DIAGNOSIS_TABLE")
    patients_table: str = Field("patients", env="PATIENTS_TABLE")
    medical_claims_table: str = Field("medical_claims", env="MEDICAL_CLAIMS_TABLE")
    pharmacy_claims_table: str = Field("pharmacy_claims", env="PHARMACY_CLAIMS_TABLE")
    icd10_codes_table: str = Field("icd10_codes", env="ICD10_CODES_TABLE")
    
    # ML settings
    model_registry_name: str = Field("genetic_testing_models", env="MODEL_REGISTRY_NAME")
    experiment_name: str = Field("/genetic_testing_experiments", env="EXPERIMENT_NAME")
    
    # Processing settings
    batch_size: int = Field(10000, env="BATCH_SIZE")
    max_workers: int = Field(4, env="MAX_WORKERS")
    
    # Feature engineering
    lookback_days: int = Field(365, env="LOOKBACK_DAYS")
    min_claims_threshold: int = Field(3, env="MIN_CLAIMS_THRESHOLD")
    
    # Model parameters
    similarity_threshold: float = Field(0.7, env="SIMILARITY_THRESHOLD")
    risk_threshold: float = Field(0.5, env="RISK_THRESHOLD")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


@dataclass
class ModelConfig:
    """Model-specific configuration parameters."""
    
    # Patient profiling
    profile_features: Dict[str, Any] = None
    clustering_algorithm: str = "kmeans"
    n_clusters: int = 10
    
    # Similarity matching
    similarity_method: str = "cosine"
    embedding_dim: int = 128
    use_pretrained_embeddings: bool = True
    
    # Risk scoring
    risk_model_type: str = "xgboost"
    cross_validation_folds: int = 5
    hyperparameter_tuning: bool = True
    
    # Feature selection
    feature_selection_method: str = "mutual_info"
    max_features: int = 100
    
    def __post_init__(self):
        if self.profile_features is None:
            self.profile_features = {
                "demographic": True,
                "claim_frequency": True,
                "cost_patterns": True,
                "provider_patterns": True,
                "temporal_patterns": True,
                "drug_interactions": True,
                "comorbidity_patterns": True
            }


class ConfigManager:
    """Centralized configuration management."""
    
    def __init__(self, config_dir: Optional[str] = None):
        self.config_dir = Path(config_dir) if config_dir else Path("config")
        self.settings = Settings()
        self.model_config = self._load_model_config()
        
    def _load_model_config(self) -> ModelConfig:
        """Load model configuration from YAML file."""
        config_file = self.config_dir / "model_config.yaml"
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                config_data = yaml.safe_load(f)
            return ModelConfig(**config_data)
        else:
            # Return default configuration
            return ModelConfig()
    
    def get_table_name(self, table_type: str) -> str:
        """Get fully qualified table name."""
        table_mapping = {
            "diagnosis": self.settings.diagnosis_table,
            "patients": self.settings.patients_table,
            "medical_claims": self.settings.medical_claims_table,
            "pharmacy_claims": self.settings.pharmacy_claims_table,
            "icd10_codes": self.settings.icd10_codes_table
        }

        table_name = table_mapping.get(table_type)
        if not table_name:
            raise ValueError(f"Unknown table type: {table_type}")

        return f"{self.settings.catalog_name}.{self.settings.database_name}.{table_name}"
    
    def get_databricks_config(self) -> Dict[str, str]:
        """Get Databricks connection configuration."""
        return {
            "host": self.settings.databricks_host,
            "token": self.settings.databricks_token,
            "cluster_id": self.settings.databricks_cluster_id
        }
    
    def get_spark_config(self) -> Dict[str, str]:
        """Get Spark configuration for Databricks."""
        return {
            "spark.sql.adaptive.enabled": "true",
            "spark.sql.adaptive.coalescePartitions.enabled": "true",
            "spark.sql.adaptive.skewJoin.enabled": "true",
            "spark.serializer": "org.apache.spark.serializer.KryoSerializer",
            "spark.sql.execution.arrow.pyspark.enabled": "true",
            "spark.databricks.delta.preview.enabled": "true"
        }
    
    def save_model_config(self, config: ModelConfig):
        """Save model configuration to YAML file."""
        config_file = self.config_dir / "model_config.yaml"
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Convert dataclass to dict
        config_dict = {
            "profile_features": config.profile_features,
            "clustering_algorithm": config.clustering_algorithm,
            "n_clusters": config.n_clusters,
            "similarity_method": config.similarity_method,
            "embedding_dim": config.embedding_dim,
            "use_pretrained_embeddings": config.use_pretrained_embeddings,
            "risk_model_type": config.risk_model_type,
            "cross_validation_folds": config.cross_validation_folds,
            "hyperparameter_tuning": config.hyperparameter_tuning,
            "feature_selection_method": config.feature_selection_method,
            "max_features": config.max_features
        }
        
        with open(config_file, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False)


# Global configuration instance
config_manager = ConfigManager()


def get_config() -> ConfigManager:
    """Get the global configuration manager instance."""
    return config_manager


def load_config_from_file(file_path: str) -> Dict[str, Any]:
    """Load configuration from a YAML file."""
    with open(file_path, 'r') as f:
        return yaml.safe_load(f)


def validate_config(config: Dict[str, Any]) -> bool:
    """Validate configuration parameters."""
    required_keys = [
        "databricks_host",
        "databricks_token", 
        "database_name"
    ]
    
    for key in required_keys:
        if key not in config:
            raise ValueError(f"Missing required configuration key: {key}")
    
    # Validate numeric parameters
    numeric_params = {
        "batch_size": (1, 100000),
        "lookback_days": (1, 3650),
        "similarity_threshold": (0.0, 1.0),
        "risk_threshold": (0.0, 1.0)
    }
    
    for param, (min_val, max_val) in numeric_params.items():
        if param in config:
            value = config[param]
            if not isinstance(value, (int, float)) or not min_val <= value <= max_val:
                raise ValueError(f"Invalid value for {param}: {value}. Must be between {min_val} and {max_val}")
    
    return True
