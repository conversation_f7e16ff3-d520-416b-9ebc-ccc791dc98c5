# Databricks Configuration for Genetic Testing ML Pipeline
# Updated for HealthVerity Claims Sample Dataset

# Databricks Connection Settings
databricks:
  host: "adb-3961822481655844.4.azuredatabricks.net"
  token: "${DATABRICKS_TOKEN}"  # Set via environment variable (optional when running inside Databricks)
  cluster_id: "${DATABRICKS_CLUSTER_ID}"  # Set via environment variable (optional when running inside Databricks)
  http_path: "/sql/1.0/warehouses/3b64ec91bab74a2f"

# Database and Table Configuration
database:
  catalog_name: "healthverity_claims_sample_patient_dataset"
  database_name: "hv_claims_sample"

  tables:
    diagnosis: "diagnosis"
    patients: "patients"
    medical_claims: "medical_claims"
    pharmacy_claims: "pharmacy_claims"
    icd10_codes: "icd10_codes"
    patient_profiles: "patient_profiles"
    recommendations: "genetic_testing_recommendations"

# HealthVerity Column Mappings
column_mappings:
  # Diagnosis table
  diagnosis_table: "healthverity_claims_sample_patient_dataset.hv_claims_sample.diagnosis"
  patient_id: "patient_id"
  icd_code: "diagnosis_code"
  diagnosis_date: "date_service"

  # Medical claims table
  medical_claims_table: "healthverity_claims_sample_patient_dataset.hv_claims_sample.medical_claim"
  claim_id: "claim_id"
  service_start_date: "date_service"
  service_end_date: "date_service_end"
  location_of_care: "location_of_care"
  pay_type: "pay_type"

  # Pharmacy claims table
  pharmacy_claims_table: "healthverity_claims_sample_patient_dataset.hv_claims_sample.pharmacy_claim"
  pharmacy_claim_id: "claim_id"
  pharmacy_service_date: "date_service"
  ndc_code: "ndc_code"
  quantity: "quantity"
  days_supply: "days_supply"

# Data Processing Settings
processing:
  batch_size: 10000
  max_workers: 4
  checkpoint_location: "/tmp/genetic_testing_checkpoints"
  
  # Data quality settings
  data_quality:
    enable_validation: true
    max_null_percentage: 0.1
    min_record_count: 1000

# Feature Engineering
features:
  lookback_days: 365  # How far back to look for claims
  min_claims_threshold: 3  # Minimum claims to include patient
  
  # Temporal features
  temporal_windows: [30, 90, 180, 365]  # Days
  
  # Aggregation features
  aggregations:
    - "count"
    - "sum" 
    - "mean"
    - "std"
    - "min"
    - "max"

# Model Registry and Experiments
mlflow:
  experiment_name: "/genetic_testing_experiments"
  model_registry_name: "genetic_testing_models"
  
  # Model versioning
  auto_log: true
  log_models: true
  log_artifacts: true

# Spark Configuration
spark:
  config:
    "spark.sql.adaptive.enabled": "true"
    "spark.sql.adaptive.coalescePartitions.enabled": "true"
    "spark.sql.adaptive.skewJoin.enabled": "true"
    "spark.serializer": "org.apache.spark.serializer.KryoSerializer"
    "spark.sql.execution.arrow.pyspark.enabled": "true"
    "spark.databricks.delta.preview.enabled": "true"
    "spark.sql.adaptive.advisoryPartitionSizeInBytes": "128MB"

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Log destinations
  handlers:
    - type: "console"
    - type: "file"
      filename: "/tmp/genetic_testing.log"
      max_bytes: 10485760  # 10MB
      backup_count: 5

# Security and Compliance
security:
  # Data encryption
  encrypt_at_rest: true
  encrypt_in_transit: true
  
  # Access control
  enable_rbac: true
  audit_logging: true
  
  # Data masking for sensitive fields
  mask_sensitive_data: true
  sensitive_fields:
    - "patient_id"
    - "date_of_birth"
    - "zip_code"

# Performance Tuning
performance:
  # Caching strategy
  cache_intermediate_results: true
  cache_storage_level: "MEMORY_AND_DISK_SER"
  
  # Partitioning strategy
  partition_columns:
    patients: ["enrollment_year"]
    medical_claims: ["service_year", "service_month"]
    pharmacy_claims: ["fill_year", "fill_month"]
  
  # Optimization settings
  optimize_writes: true
  auto_compact: true
  z_order_columns:
    medical_claims: ["patient_id", "service_date"]
    pharmacy_claims: ["patient_id", "fill_date"]
