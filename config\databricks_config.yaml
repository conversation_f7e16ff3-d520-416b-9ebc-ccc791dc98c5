# Databricks Configuration for Genetic Testing ML Pipeline

# Databricks Connection Settings
databricks:
  host: "https://your-workspace.cloud.databricks.com"
  token: "${DATABRICKS_TOKEN}"  # Set via environment variable
  cluster_id: "${DATABRICKS_CLUSTER_ID}"  # Set via environment variable

# Database and Table Configuration
database:
  catalog_name: "main"
  database_name: "genetic_testing"
  
  tables:
    patients: "patients"
    medical_claims: "medical_claims"
    pharmacy_claims: "pharmacy_claims"
    icd10_codes: "icd10_codes"
    patient_profiles: "patient_profiles"
    recommendations: "genetic_testing_recommendations"

# Data Processing Settings
processing:
  batch_size: 10000
  max_workers: 4
  checkpoint_location: "/tmp/genetic_testing_checkpoints"
  
  # Data quality settings
  data_quality:
    enable_validation: true
    max_null_percentage: 0.1
    min_record_count: 1000

# Feature Engineering
features:
  lookback_days: 365  # How far back to look for claims
  min_claims_threshold: 3  # Minimum claims to include patient
  
  # Temporal features
  temporal_windows: [30, 90, 180, 365]  # Days
  
  # Aggregation features
  aggregations:
    - "count"
    - "sum" 
    - "mean"
    - "std"
    - "min"
    - "max"

# Model Registry and Experiments
mlflow:
  experiment_name: "/genetic_testing_experiments"
  model_registry_name: "genetic_testing_models"
  
  # Model versioning
  auto_log: true
  log_models: true
  log_artifacts: true

# Spark Configuration
spark:
  config:
    "spark.sql.adaptive.enabled": "true"
    "spark.sql.adaptive.coalescePartitions.enabled": "true"
    "spark.sql.adaptive.skewJoin.enabled": "true"
    "spark.serializer": "org.apache.spark.serializer.KryoSerializer"
    "spark.sql.execution.arrow.pyspark.enabled": "true"
    "spark.databricks.delta.preview.enabled": "true"
    "spark.sql.adaptive.advisoryPartitionSizeInBytes": "128MB"

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Log destinations
  handlers:
    - type: "console"
    - type: "file"
      filename: "/tmp/genetic_testing.log"
      max_bytes: 10485760  # 10MB
      backup_count: 5

# Security and Compliance
security:
  # Data encryption
  encrypt_at_rest: true
  encrypt_in_transit: true
  
  # Access control
  enable_rbac: true
  audit_logging: true
  
  # Data masking for sensitive fields
  mask_sensitive_data: true
  sensitive_fields:
    - "patient_id"
    - "date_of_birth"
    - "zip_code"

# Performance Tuning
performance:
  # Caching strategy
  cache_intermediate_results: true
  cache_storage_level: "MEMORY_AND_DISK_SER"
  
  # Partitioning strategy
  partition_columns:
    patients: ["enrollment_year"]
    medical_claims: ["service_year", "service_month"]
    pharmacy_claims: ["fill_year", "fill_month"]
  
  # Optimization settings
  optimize_writes: true
  auto_compact: true
  z_order_columns:
    medical_claims: ["patient_id", "service_date"]
    pharmacy_claims: ["patient_id", "fill_date"]
