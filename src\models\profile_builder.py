"""
Patient profile generation module.
Creates comprehensive patient profiles based on claims patterns and ML algorithms.
"""

import logging
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from sklearn.cluster import KMeans, DBSCAN
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import pandas as pd

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import (
    col, collect_list, collect_set, count, avg, stddev,
    when, size, array_intersect, array_union, explode,
    regexp_replace, lower, trim, split
)
from pyspark.ml.feature import VectorAssembler, StandardScaler as SparkStandardScaler
from pyspark.ml.clustering import KMeans as SparkKMeans
from pyspark.ml.stat import Correlation

from ..data.schemas import PatientProfile
from ..utils.config import get_config

logger = logging.getLogger(__name__)


class PatientProfileBuilder:
    """Builds comprehensive patient profiles using ML algorithms."""
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = get_config()
        self.scaler = StandardScaler()
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        
    def build_genetic_patient_profiles(self, 
                                     genetic_patients_df: DataFrame,
                                     patient_features_df: DataFrame) -> DataFrame:
        """
        Build comprehensive profiles for patients with genetic diagnoses.
        
        Args:
            genetic_patients_df: DataFrame with genetic patients
            patient_features_df: DataFrame with aggregated patient features
            
        Returns:
            DataFrame with detailed patient profiles
        """
        logger.info("Building genetic patient profiles")
        
        # Join genetic patients with their features
        genetic_profiles = genetic_patients_df.join(
            patient_features_df, "patient_id", "inner"
        )
        
        # Add profile-specific features
        genetic_profiles = self._add_profile_features(genetic_profiles)
        
        # Perform clustering analysis
        genetic_profiles = self._perform_clustering_analysis(genetic_profiles)
        
        # Calculate similarity scores
        genetic_profiles = self._calculate_profile_similarities(genetic_profiles)
        
        # Generate profile summaries
        genetic_profiles = self._generate_profile_summaries(genetic_profiles)
        
        logger.info(f"Built profiles for {genetic_profiles.count()} genetic patients")
        
        return genetic_profiles
    
    def _add_profile_features(self, df: DataFrame) -> DataFrame:
        """Add profile-specific features to patient data."""
        
        # Calculate claim frequency score
        df = df.withColumn("claim_frequency_score",
                          (col("medical_claim_count") + col("pharmacy_claim_count")) / 365.0)
        
        # Calculate cost intensity score
        df = df.withColumn("cost_intensity_score",
                          (col("total_medical_cost") + col("total_pharmacy_cost")) / 
                          (col("medical_claim_count") + col("pharmacy_claim_count")))
        
        # Calculate provider diversity score
        df = df.withColumn("provider_diversity_score",
                          col("unique_providers") + col("unique_specialties"))
        
        # Calculate medication complexity score
        df = df.withColumn("medication_complexity_score",
                          col("unique_medications") * col("unique_drug_classes") / 100.0)
        
        # Calculate care coordination score
        df = df.withColumn("care_coordination_score",
                          when(col("unique_providers") > 0,
                               col("specialist_visits") / col("unique_providers"))
                          .otherwise(0.0))
        
        return df
    
    def _perform_clustering_analysis(self, df: DataFrame) -> DataFrame:
        """Perform clustering analysis to identify patient segments."""
        
        # Select features for clustering
        feature_cols = [
            "claim_frequency_score", "cost_intensity_score", 
            "provider_diversity_score", "medication_complexity_score",
            "care_coordination_score", "avg_complexity_score",
            "specialist_visit_ratio", "chronic_medication_ratio"
        ]
        
        # Assemble features
        assembler = VectorAssembler(inputCols=feature_cols, outputCol="features")
        df_features = assembler.transform(df)
        
        # Scale features
        scaler = SparkStandardScaler(inputCol="features", outputCol="scaled_features")
        scaler_model = scaler.fit(df_features)
        df_scaled = scaler_model.transform(df_features)
        
        # Perform K-means clustering
        n_clusters = self.config.model_config.n_clusters
        kmeans = SparkKMeans(k=n_clusters, featuresCol="scaled_features", predictionCol="cluster")
        kmeans_model = kmeans.fit(df_scaled)
        df_clustered = kmeans_model.transform(df_scaled)
        
        # Add cluster interpretation
        df_clustered = self._interpret_clusters(df_clustered)
        
        return df_clustered
    
    def _interpret_clusters(self, df: DataFrame) -> DataFrame:
        """Interpret and label clusters based on characteristics."""
        
        # Calculate cluster statistics
        cluster_stats = (
            df.groupBy("cluster")
            .agg(
                avg("claim_frequency_score").alias("avg_claim_frequency"),
                avg("cost_intensity_score").alias("avg_cost_intensity"),
                avg("provider_diversity_score").alias("avg_provider_diversity"),
                avg("medication_complexity_score").alias("avg_medication_complexity"),
                count("*").alias("cluster_size")
            )
        ).collect()
        
        # Create cluster labels based on characteristics
        cluster_labels = {}
        for row in cluster_stats:
            cluster_id = row.cluster
            
            if row.avg_cost_intensity > 1000 and row.avg_provider_diversity > 5:
                label = "high_complexity_high_cost"
            elif row.avg_claim_frequency > 1.0 and row.avg_medication_complexity > 0.5:
                label = "frequent_users_complex_medications"
            elif row.avg_provider_diversity > 3 and row.avg_cost_intensity < 500:
                label = "coordinated_care_moderate_cost"
            elif row.avg_claim_frequency < 0.5:
                label = "low_utilization"
            else:
                label = "standard_care_pattern"
            
            cluster_labels[cluster_id] = label
        
        # Add cluster labels to DataFrame
        for cluster_id, label in cluster_labels.items():
            df = df.withColumn("cluster_label",
                              when(col("cluster") == cluster_id, label)
                              .otherwise(col("cluster_label")))
        
        return df
    
    def _calculate_profile_similarities(self, df: DataFrame) -> DataFrame:
        """Calculate similarity scores between patient profiles."""
        
        # Convert to Pandas for similarity calculations
        pandas_df = df.select([
            "patient_id", "genetic_categories", "all_primary_diagnoses",
            "all_therapeutic_classes", "all_provider_specialties"
        ]).toPandas()
        
        # Calculate diagnosis similarity matrix
        diagnosis_similarity = self._calculate_diagnosis_similarity(pandas_df)
        
        # Calculate medication similarity matrix
        medication_similarity = self._calculate_medication_similarity(pandas_df)
        
        # Combine similarities
        combined_similarity = (diagnosis_similarity + medication_similarity) / 2
        
        # Add similarity scores back to Spark DataFrame
        similarity_df = self._create_similarity_dataframe(pandas_df, combined_similarity)
        
        # Join back with main DataFrame
        result_df = df.join(similarity_df, "patient_id", "left")
        
        return result_df
    
    def _calculate_diagnosis_similarity(self, pandas_df: pd.DataFrame) -> np.ndarray:
        """Calculate diagnosis-based similarity between patients."""
        
        # Create diagnosis text for each patient
        diagnosis_texts = []
        for _, row in pandas_df.iterrows():
            diagnoses = row['all_primary_diagnoses'] if row['all_primary_diagnoses'] else []
            diagnosis_text = ' '.join(diagnoses) if diagnoses else ''
            diagnosis_texts.append(diagnosis_text)
        
        # Calculate TF-IDF similarity
        if any(diagnosis_texts):
            tfidf_matrix = self.vectorizer.fit_transform(diagnosis_texts)
            similarity_matrix = cosine_similarity(tfidf_matrix)
        else:
            similarity_matrix = np.zeros((len(pandas_df), len(pandas_df)))
        
        return similarity_matrix
    
    def _calculate_medication_similarity(self, pandas_df: pd.DataFrame) -> np.ndarray:
        """Calculate medication-based similarity between patients."""
        
        # Create medication text for each patient
        medication_texts = []
        for _, row in pandas_df.iterrows():
            medications = row['all_therapeutic_classes'] if row['all_therapeutic_classes'] else []
            medication_text = ' '.join(medications) if medications else ''
            medication_texts.append(medication_text)
        
        # Calculate TF-IDF similarity
        if any(medication_texts):
            vectorizer = TfidfVectorizer(max_features=500)
            tfidf_matrix = vectorizer.fit_transform(medication_texts)
            similarity_matrix = cosine_similarity(tfidf_matrix)
        else:
            similarity_matrix = np.zeros((len(pandas_df), len(pandas_df)))
        
        return similarity_matrix
    
    def _create_similarity_dataframe(self, 
                                   pandas_df: pd.DataFrame, 
                                   similarity_matrix: np.ndarray) -> DataFrame:
        """Create Spark DataFrame with similarity scores."""
        
        similarity_data = []
        
        for i, patient_id in enumerate(pandas_df['patient_id']):
            # Get top 10 most similar patients (excluding self)
            similarities = similarity_matrix[i]
            similar_indices = np.argsort(similarities)[::-1][1:11]  # Exclude self, top 10
            
            similar_patients = []
            similarity_scores = []
            
            for idx in similar_indices:
                if similarities[idx] > 0.1:  # Minimum similarity threshold
                    similar_patients.append(pandas_df.iloc[idx]['patient_id'])
                    similarity_scores.append(float(similarities[idx]))
            
            similarity_data.append({
                'patient_id': patient_id,
                'similar_patients': similar_patients,
                'similarity_scores': similarity_scores,
                'avg_similarity': np.mean(similarity_scores) if similarity_scores else 0.0
            })
        
        # Convert to Spark DataFrame
        similarity_df = self.spark.createDataFrame(similarity_data)
        
        return similarity_df
    
    def _generate_profile_summaries(self, df: DataFrame) -> DataFrame:
        """Generate human-readable profile summaries."""
        
        df = df.withColumn("profile_summary",
                          concat_ws("; ",
                                   concat(lit("Cluster: "), col("cluster_label")),
                                   concat(lit("Genetic Categories: "), 
                                         array_join(col("genetic_categories"), ", ")),
                                   concat(lit("Claim Frequency: "), 
                                         round(col("claim_frequency_score"), 2)),
                                   concat(lit("Cost Intensity: $"), 
                                         round(col("cost_intensity_score"), 0)),
                                   concat(lit("Provider Diversity: "), 
                                         col("provider_diversity_score"))))
        
        return df
    
    def identify_common_patterns(self, genetic_profiles_df: DataFrame) -> Dict[str, Any]:
        """
        Identify common patterns across genetic disorder patients.
        
        Args:
            genetic_profiles_df: DataFrame with genetic patient profiles
            
        Returns:
            Dictionary with common patterns analysis
        """
        logger.info("Identifying common patterns in genetic patients")
        
        # Convert to Pandas for analysis
        pandas_df = genetic_profiles_df.toPandas()
        
        patterns = {
            "cluster_distribution": self._analyze_cluster_distribution(pandas_df),
            "common_diagnoses": self._analyze_common_diagnoses(pandas_df),
            "common_medications": self._analyze_common_medications(pandas_df),
            "provider_patterns": self._analyze_provider_patterns(pandas_df),
            "cost_patterns": self._analyze_cost_patterns(pandas_df),
            "temporal_patterns": self._analyze_temporal_patterns(pandas_df)
        }
        
        return patterns
    
    def _analyze_cluster_distribution(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze distribution of patients across clusters."""
        
        cluster_counts = df['cluster_label'].value_counts()
        cluster_stats = df.groupby('cluster_label').agg({
            'claim_frequency_score': ['mean', 'std'],
            'cost_intensity_score': ['mean', 'std'],
            'provider_diversity_score': ['mean', 'std']
        }).round(2)
        
        return {
            "cluster_counts": cluster_counts.to_dict(),
            "cluster_statistics": cluster_stats.to_dict()
        }
    
    def _analyze_common_diagnoses(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze most common diagnoses across genetic patients."""
        
        all_diagnoses = []
        for diagnoses_list in df['all_primary_diagnoses'].dropna():
            if diagnoses_list:
                all_diagnoses.extend(diagnoses_list)
        
        diagnosis_counts = pd.Series(all_diagnoses).value_counts().head(20)
        
        return diagnosis_counts.to_dict()
    
    def _analyze_common_medications(self, df: pd.DataFrame) -> Dict[str, int]:
        """Analyze most common medication classes across genetic patients."""
        
        all_medications = []
        for med_list in df['all_therapeutic_classes'].dropna():
            if med_list:
                all_medications.extend(med_list)
        
        medication_counts = pd.Series(all_medications).value_counts().head(20)
        
        return medication_counts.to_dict()
    
    def _analyze_provider_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze provider utilization patterns."""
        
        provider_stats = {
            "avg_unique_providers": df['unique_providers'].mean(),
            "avg_specialist_ratio": df['specialist_visit_ratio'].mean(),
            "common_specialties": {}
        }
        
        all_specialties = []
        for specialty_list in df['all_provider_specialties'].dropna():
            if specialty_list:
                all_specialties.extend(specialty_list)
        
        specialty_counts = pd.Series(all_specialties).value_counts().head(10)
        provider_stats["common_specialties"] = specialty_counts.to_dict()
        
        return provider_stats
    
    def _analyze_cost_patterns(self, df: pd.DataFrame) -> Dict[str, float]:
        """Analyze cost patterns across genetic patients."""
        
        return {
            "avg_total_medical_cost": df['total_medical_cost'].mean(),
            "avg_total_pharmacy_cost": df['total_pharmacy_cost'].mean(),
            "avg_cost_per_claim": df['cost_intensity_score'].mean(),
            "cost_std": df['cost_intensity_score'].std()
        }
    
    def _analyze_temporal_patterns(self, df: pd.DataFrame) -> Dict[str, float]:
        """Analyze temporal patterns in care utilization."""
        
        return {
            "avg_claim_frequency": df['claim_frequency_score'].mean(),
            "avg_years_with_claims": df['years_with_claims'].mean(),
            "avg_months_with_claims": df['months_with_claims'].mean()
        }
