# Databricks notebook source
# MAGIC %md
# MAGIC # Genetic Testing ML Pipeline - Data Exploration
# MAGIC 
# MAGIC This notebook explores the healthcare claims data to understand patterns and prepare for genetic testing patient identification.
# MAGIC 
# MAGIC ## Objectives
# MAGIC 1. Explore patient demographics and claims patterns
# MAGIC 2. Analyze ICD-10 code distributions
# MAGIC 3. Identify genetic disorder prevalence
# MAGIC 4. Understand claims utilization patterns

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup and Configuration

# COMMAND ----------

import sys
sys.path.append('/Workspace/genetic_testing/src')

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# Import our modules
from data.loaders import DataLoader
from models.icd_classifier import ICD10GeneticClassifier
from utils.config import get_config

# Initialize components
config = get_config()
data_loader = DataLoader(spark)
icd_classifier = ICD10GeneticClassifier(spark)

print("Setup completed successfully!")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Load and Examine Data

# COMMAND ----------

# Load datasets
print("Loading datasets...")

patients_df = data_loader.load_patients()
medical_claims_df = data_loader.load_medical_claims()
pharmacy_claims_df = data_loader.load_pharmacy_claims()

print(f"Patients: {patients_df.count():,}")
print(f"Medical Claims: {medical_claims_df.count():,}")
print(f"Pharmacy Claims: {pharmacy_claims_df.count():,}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Patient Demographics Analysis

# COMMAND ----------

# Analyze patient demographics
print("=== PATIENT DEMOGRAPHICS ===")

# Age distribution
patients_with_age = patients_df.withColumn(
    "age", 
    floor(datediff(current_date(), col("date_of_birth")) / 365.25)
).filter(col("age").between(0, 120))

age_stats = patients_with_age.select("age").describe()
age_stats.show()

# Gender distribution
gender_dist = patients_df.groupBy("gender").count().orderBy(desc("count"))
gender_dist.show()

# Race/ethnicity distribution
race_dist = patients_df.groupBy("race").count().orderBy(desc("count"))
race_dist.show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Medical Claims Analysis

# COMMAND ----------

# Analyze medical claims patterns
print("=== MEDICAL CLAIMS ANALYSIS ===")

# Claims per patient
claims_per_patient = (
    medical_claims_df
    .groupBy("patient_id")
    .agg(
        count("*").alias("claim_count"),
        sum("paid_amount").alias("total_cost"),
        countDistinct("icd10_primary").alias("unique_diagnoses")
    )
)

claims_stats = claims_per_patient.select("claim_count", "total_cost", "unique_diagnoses").describe()
claims_stats.show()

# Top ICD-10 codes
top_icd_codes = (
    medical_claims_df
    .groupBy("icd10_primary")
    .count()
    .orderBy(desc("count"))
    .limit(20)
)

print("Top 20 ICD-10 Codes:")
top_icd_codes.show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Genetic Disorder Analysis

# COMMAND ----------

# Identify genetic disorder patients
print("=== GENETIC DISORDER ANALYSIS ===")

genetic_patients_df = icd_classifier.identify_genetic_patients(medical_claims_df)

print(f"Patients with genetic diagnoses: {genetic_patients_df.count():,}")

# Genetic categories distribution
genetic_categories = (
    genetic_patients_df
    .select(explode("genetic_categories").alias("category"))
    .groupBy("category")
    .count()
    .orderBy(desc("count"))
)

print("Genetic Categories Distribution:")
genetic_categories.show()

# Most common genetic ICD codes
genetic_icd_codes = (
    genetic_patients_df
    .select(explode("genetic_icd_codes").alias("icd_code"))
    .groupBy("icd_code")
    .count()
    .orderBy(desc("count"))
    .limit(15)
)

print("Most Common Genetic ICD Codes:")
genetic_icd_codes.show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Claims Utilization Patterns

# COMMAND ----------

# Analyze utilization patterns for genetic vs non-genetic patients
print("=== UTILIZATION PATTERNS ===")

genetic_patient_ids = [row.patient_id for row in genetic_patients_df.select("patient_id").collect()]

# Compare utilization between genetic and non-genetic patients
utilization_comparison = (
    claims_per_patient
    .withColumn("patient_type", 
                when(col("patient_id").isin(genetic_patient_ids), "genetic")
                .otherwise("non_genetic"))
    .groupBy("patient_type")
    .agg(
        avg("claim_count").alias("avg_claims"),
        avg("total_cost").alias("avg_cost"),
        avg("unique_diagnoses").alias("avg_diagnoses"),
        count("*").alias("patient_count")
    )
)

print("Utilization Comparison:")
utilization_comparison.show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Pharmacy Claims Analysis

# COMMAND ----------

# Analyze pharmacy claims
print("=== PHARMACY CLAIMS ANALYSIS ===")

# Pharmacy utilization
pharmacy_per_patient = (
    pharmacy_claims_df
    .groupBy("patient_id")
    .agg(
        count("*").alias("rx_count"),
        sum("paid_amount").alias("total_rx_cost"),
        countDistinct("therapeutic_class").alias("unique_drug_classes")
    )
)

pharmacy_stats = pharmacy_per_patient.select("rx_count", "total_rx_cost", "unique_drug_classes").describe()
pharmacy_stats.show()

# Top therapeutic classes
top_drug_classes = (
    pharmacy_claims_df
    .groupBy("therapeutic_class")
    .count()
    .orderBy(desc("count"))
    .limit(15)
)

print("Top Therapeutic Classes:")
top_drug_classes.show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Data Quality Assessment

# COMMAND ----------

# Assess data quality
print("=== DATA QUALITY ASSESSMENT ===")

# Check for missing values in key fields
def check_missing_values(df, table_name):
    print(f"\n{table_name} - Missing Values:")
    total_rows = df.count()
    
    for col_name in df.columns:
        null_count = df.filter(col(col_name).isNull()).count()
        null_percentage = (null_count / total_rows) * 100
        print(f"  {col_name}: {null_count:,} ({null_percentage:.2f}%)")

check_missing_values(patients_df, "Patients")
check_missing_values(medical_claims_df.limit(10000), "Medical Claims (sample)")
check_missing_values(pharmacy_claims_df.limit(10000), "Pharmacy Claims (sample)")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Temporal Analysis

# COMMAND ----------

# Analyze temporal patterns
print("=== TEMPORAL ANALYSIS ===")

# Claims by month
monthly_claims = (
    medical_claims_df
    .withColumn("year_month", date_format("service_date", "yyyy-MM"))
    .groupBy("year_month")
    .agg(
        count("*").alias("claim_count"),
        sum("paid_amount").alias("total_cost")
    )
    .orderBy("year_month")
)

print("Monthly Claims Volume:")
monthly_claims.show()

# Genetic diagnoses by month
genetic_claims_monthly = (
    medical_claims_df
    .filter(col("icd10_primary").rlike("^(Z8[0-4]|Q9[0-9]|E7[0-9])"))
    .withColumn("year_month", date_format("service_date", "yyyy-MM"))
    .groupBy("year_month")
    .count()
    .orderBy("year_month")
)

print("Monthly Genetic Diagnoses:")
genetic_claims_monthly.show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Summary and Insights

# COMMAND ----------

print("=== EXPLORATION SUMMARY ===")
print(f"Total Patients: {patients_df.count():,}")
print(f"Total Medical Claims: {medical_claims_df.count():,}")
print(f"Total Pharmacy Claims: {pharmacy_claims_df.count():,}")
print(f"Patients with Genetic Diagnoses: {genetic_patients_df.count():,}")

genetic_prevalence = (genetic_patients_df.count() / patients_df.count()) * 100
print(f"Genetic Diagnosis Prevalence: {genetic_prevalence:.2f}%")

# Key insights
print("\n=== KEY INSIGHTS ===")
print("1. Data quality appears good with minimal missing values")
print("2. Genetic disorder patients show different utilization patterns")
print("3. Family history codes (Z80-Z84) are most common genetic indicators")
print("4. Temporal patterns show consistent claims volume")
print("5. Ready to proceed with ML model development")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Next Steps
# MAGIC 
# MAGIC Based on this exploration:
# MAGIC 1. **Data Quality**: Overall good, proceed with current dataset
# MAGIC 2. **Genetic Prevalence**: Sufficient genetic patients for model training
# MAGIC 3. **Feature Engineering**: Focus on utilization patterns and temporal features
# MAGIC 4. **Model Development**: Ready to build patient profiles and similarity models
# MAGIC 
# MAGIC Continue to notebook `02_model_development.py` for ML model building.
