# Genetic Testing ML Pipeline - API Reference

This document provides comprehensive API reference for the genetic testing ML pipeline components.

## Core Components

### DataLoader

The `DataLoader` class handles loading and basic validation of healthcare data.

```python
from src.data.loaders import DataLoader

loader = DataLoader(spark)
```

#### Methods

##### `load_patients(filter_conditions=None, limit=None)`
Load patient demographic data.

**Parameters:**
- `filter_conditions` (str, optional): SQL WHERE clause conditions
- `limit` (int, optional): Maximum number of records to return

**Returns:** Spark DataFrame with patient data

**Example:**
```python
# Load all patients
patients_df = loader.load_patients()

# Load patients with filter
patients_df = loader.load_patients("enrollment_start_date >= '2023-01-01'")
```

##### `load_medical_claims(patient_ids=None, start_date=None, end_date=None, icd_codes=None)`
Load medical claims data with optional filtering.

**Parameters:**
- `patient_ids` (List[str], optional): List of patient IDs to filter
- `start_date` (str, optional): Start date for service_date filter (YYYY-MM-DD)
- `end_date` (str, optional): End date for service_date filter (YYYY-MM-DD)
- `icd_codes` (List[str], optional): List of ICD-10 codes to filter

**Returns:** Spark DataFrame with medical claims

**Example:**
```python
# Load claims for specific patients
claims_df = loader.load_medical_claims(
    patient_ids=['P001', 'P002'],
    start_date='2023-01-01',
    end_date='2023-12-31'
)
```

##### `load_pharmacy_claims(patient_ids=None, start_date=None, end_date=None, drug_classes=None)`
Load pharmacy claims data with optional filtering.

**Parameters:**
- `patient_ids` (List[str], optional): List of patient IDs to filter
- `start_date` (str, optional): Start date for fill_date filter (YYYY-MM-DD)
- `end_date` (str, optional): End date for fill_date filter (YYYY-MM-DD)
- `drug_classes` (List[str], optional): List of therapeutic classes to filter

**Returns:** Spark DataFrame with pharmacy claims

### ICD10GeneticClassifier

The `ICD10GeneticClassifier` identifies patients with genetic disorder diagnoses.

```python
from src.models.icd_classifier import ICD10GeneticClassifier

classifier = ICD10GeneticClassifier(spark)
```

#### Methods

##### `classify_icd_code(icd_code)`
Classify a single ICD-10 code for genetic relevance.

**Parameters:**
- `icd_code` (str): ICD-10 code to classify

**Returns:** List[str] of genetic categories that match the code

**Example:**
```python
categories = classifier.classify_icd_code("Z80.0")
# Returns: ["family_history_malignant"]
```

##### `identify_genetic_patients(medical_claims_df, require_primary_diagnosis=False, min_genetic_claims=1)`
Identify patients with genetic disorder diagnoses.

**Parameters:**
- `medical_claims_df` (DataFrame): DataFrame with medical claims
- `require_primary_diagnosis` (bool): If True, only consider primary diagnoses
- `min_genetic_claims` (int): Minimum number of genetic claims required

**Returns:** DataFrame with genetic patients and their classifications

**Example:**
```python
genetic_patients = classifier.identify_genetic_patients(
    medical_claims_df,
    min_genetic_claims=2
)
```

### ClaimsProcessor

The `ClaimsProcessor` handles feature engineering and data processing.

```python
from src.data.processors import ClaimsProcessor

processor = ClaimsProcessor(spark)
```

#### Methods

##### `process_medical_claims(medical_claims_df, patient_ids=None)`
Process medical claims data with feature engineering.

**Parameters:**
- `medical_claims_df` (DataFrame): Raw medical claims DataFrame
- `patient_ids` (List[str], optional): Optional list of patient IDs to filter

**Returns:** DataFrame with processed medical claims and engineered features

##### `aggregate_patient_features(medical_claims_df, pharmacy_claims_df, lookback_days=365)`
Aggregate claims data to create patient-level features.

**Parameters:**
- `medical_claims_df` (DataFrame): Processed medical claims
- `pharmacy_claims_df` (DataFrame): Processed pharmacy claims
- `lookback_days` (int): Number of days to look back

**Returns:** DataFrame with patient-level aggregated features

### PatientProfileBuilder

The `PatientProfileBuilder` creates comprehensive patient profiles using ML algorithms.

```python
from src.models.profile_builder import PatientProfileBuilder

profile_builder = PatientProfileBuilder(spark)
```

#### Methods

##### `build_genetic_patient_profiles(genetic_patients_df, patient_features_df)`
Build comprehensive profiles for patients with genetic diagnoses.

**Parameters:**
- `genetic_patients_df` (DataFrame): DataFrame with genetic patients
- `patient_features_df` (DataFrame): DataFrame with aggregated patient features

**Returns:** DataFrame with detailed patient profiles

##### `identify_common_patterns(genetic_profiles_df)`
Identify common patterns across genetic disorder patients.

**Parameters:**
- `genetic_profiles_df` (DataFrame): DataFrame with genetic patient profiles

**Returns:** Dictionary with common patterns analysis

### SimilarityMatcher

The `SimilarityMatcher` finds undiagnosed patients similar to genetic disorder patients.

```python
from src.models.similarity import SimilarityMatcher

matcher = SimilarityMatcher(spark)
```

#### Methods

##### `find_similar_undiagnosed_patients(genetic_profiles_df, all_patient_features_df, similarity_method="cosine")`
Find undiagnosed patients similar to genetic disorder patients.

**Parameters:**
- `genetic_profiles_df` (DataFrame): DataFrame with genetic patient profiles
- `all_patient_features_df` (DataFrame): DataFrame with all patient features
- `similarity_method` (str): Similarity calculation method ("cosine", "euclidean", "knn")

**Returns:** DataFrame with similar undiagnosed patients and their similarity scores

### GeneticRiskScorer

The `GeneticRiskScorer` scores patients for genetic disorder risk using ML models.

```python
from src.models.recommender import GeneticRiskScorer

scorer = GeneticRiskScorer(spark)
```

#### Methods

##### `train_risk_model(genetic_patients_df, control_patients_df, features_df)`
Train risk scoring model using genetic and control patients.

**Parameters:**
- `genetic_patients_df` (DataFrame): DataFrame with genetic patients (positive cases)
- `control_patients_df` (DataFrame): DataFrame with control patients (negative cases)
- `features_df` (DataFrame): DataFrame with patient features

**Returns:** Dictionary with training results and model performance

##### `score_patients(patients_df)`
Score patients for genetic disorder risk.

**Parameters:**
- `patients_df` (DataFrame): DataFrame with patient features

**Returns:** DataFrame with risk scores

### GeneticTestingRecommendationEngine

The `GeneticTestingRecommendationEngine` generates genetic testing recommendations.

```python
from src.models.recommender import GeneticTestingRecommendationEngine

engine = GeneticTestingRecommendationEngine(spark)
```

#### Methods

##### `generate_recommendations(risk_scores_df, similarity_results_df, patient_features_df)`
Generate genetic testing recommendations.

**Parameters:**
- `risk_scores_df` (DataFrame): DataFrame with risk scores
- `similarity_results_df` (DataFrame): DataFrame with similarity results
- `patient_features_df` (DataFrame): DataFrame with patient features

**Returns:** DataFrame with recommendations

## Configuration

### Settings

The pipeline uses environment variables and configuration files for settings:

```python
from src.utils.config import get_config

config = get_config()
```

#### Environment Variables

- `DATABRICKS_HOST`: Databricks workspace URL
- `DATABRICKS_TOKEN`: Databricks access token
- `DATABRICKS_CLUSTER_ID`: Cluster ID for execution
- `DATABASE_NAME`: Database name (default: "genetic_testing")
- `BATCH_SIZE`: Processing batch size (default: 10000)
- `SIMILARITY_THRESHOLD`: Similarity threshold (default: 0.7)
- `RISK_THRESHOLD`: Risk threshold (default: 0.5)

#### Configuration Files

- `config/databricks_config.yaml`: Databricks and infrastructure settings
- `config/model_config.yaml`: Model parameters and hyperparameters

## Data Schemas

### Patient Schema
```python
PATIENT_SCHEMA = StructType([
    StructField("patient_id", StringType(), False),
    StructField("date_of_birth", DateType(), True),
    StructField("gender", StringType(), True),
    StructField("race", StringType(), True),
    StructField("ethnicity", StringType(), True),
    StructField("zip_code", StringType(), True),
    StructField("insurance_type", StringType(), True),
])
```

### Medical Claims Schema
```python
MEDICAL_CLAIMS_SCHEMA = StructType([
    StructField("claim_id", StringType(), False),
    StructField("patient_id", StringType(), False),
    StructField("service_date", DateType(), False),
    StructField("provider_id", StringType(), True),
    StructField("icd10_primary", StringType(), True),
    StructField("icd10_secondary", ArrayType(StringType()), True),
    StructField("cpt_codes", ArrayType(StringType()), True),
    StructField("claim_amount", DoubleType(), True),
    StructField("paid_amount", DoubleType(), True),
])
```

### Pharmacy Claims Schema
```python
PHARMACY_CLAIMS_SCHEMA = StructType([
    StructField("claim_id", StringType(), False),
    StructField("patient_id", StringType(), False),
    StructField("fill_date", DateType(), False),
    StructField("ndc_code", StringType(), False),
    StructField("drug_name", StringType(), True),
    StructField("therapeutic_class", StringType(), True),
    StructField("days_supply", IntegerType(), True),
    StructField("quantity", DoubleType(), True),
])
```

## Error Handling

All components include comprehensive error handling:

```python
try:
    results = pipeline.run_full_pipeline()
    if results["pipeline_status"] == "failed":
        print(f"Pipeline failed: {results['error_message']}")
except Exception as e:
    print(f"Unexpected error: {str(e)}")
```

## Performance Considerations

- Use appropriate batch sizes for large datasets
- Enable Spark adaptive query execution
- Consider data partitioning strategies
- Monitor memory usage during ML model training
- Use caching for frequently accessed DataFrames

## Security and Compliance

- All patient data should be properly de-identified
- Ensure HIPAA compliance in data handling
- Use secure connections for Databricks
- Implement proper access controls
- Audit all data access and processing
