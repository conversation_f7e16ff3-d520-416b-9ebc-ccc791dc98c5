"""
Data schemas for genetic testing ML pipeline.
Defines Spark SQL schemas and Pydantic models for data validation.
"""

from pyspark.sql.types import (
    StructType, StructField, StringType, IntegerType, 
    DoubleType, DateType, TimestampType, BooleanType, ArrayType
)
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum


class ClaimType(str, Enum):
    """Enumeration for claim types."""
    MEDICAL = "medical"
    PHARMACY = "pharmacy"
    INPATIENT = "inpatient"
    OUTPATIENT = "outpatient"
    EMERGENCY = "emergency"


class GeneticRiskLevel(str, Enum):
    """Risk levels for genetic testing recommendations."""
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"


# Spark SQL Schemas
PATIENT_SCHEMA = StructType([
    StructField("patient_id", StringType(), False),
    Struct<PERSON><PERSON>("date_of_birth", DateType(), True),
    <PERSON>ru<PERSON><PERSON><PERSON>("gender", StringType(), True),
    Struct<PERSON><PERSON>("race", StringType(), True),
    StructField("ethnicity", StringType(), True),
    StructField("zip_code", StringType(), True),
    StructField("insurance_type", StringType(), True),
    StructField("enrollment_start_date", DateType(), True),
    StructField("enrollment_end_date", DateType(), True),
])

MEDICAL_CLAIMS_SCHEMA = StructType([
    StructField("claim_id", StringType(), False),
    StructField("patient_id", StringType(), False),
    StructField("service_date", DateType(), False),
    StructField("provider_id", StringType(), True),
    StructField("provider_specialty", StringType(), True),
    StructField("icd10_primary", StringType(), True),
    StructField("icd10_secondary", ArrayType(StringType()), True),
    StructField("cpt_codes", ArrayType(StringType()), True),
    StructField("claim_amount", DoubleType(), True),
    StructField("paid_amount", DoubleType(), True),
    StructField("place_of_service", StringType(), True),
    StructField("claim_type", StringType(), True),
])

PHARMACY_CLAIMS_SCHEMA = StructType([
    StructField("claim_id", StringType(), False),
    StructField("patient_id", StringType(), False),
    StructField("fill_date", DateType(), False),
    StructField("ndc_code", StringType(), False),
    StructField("drug_name", StringType(), True),
    StructField("generic_name", StringType(), True),
    StructField("therapeutic_class", StringType(), True),
    StructField("days_supply", IntegerType(), True),
    StructField("quantity", DoubleType(), True),
    StructField("pharmacy_id", StringType(), True),
    StructField("prescriber_id", StringType(), True),
    StructField("claim_amount", DoubleType(), True),
    StructField("paid_amount", DoubleType(), True),
])

ICD10_CODES_SCHEMA = StructType([
    StructField("icd10_code", StringType(), False),
    StructField("description", StringType(), False),
    StructField("category", StringType(), True),
    StructField("is_genetic_related", BooleanType(), True),
    StructField("genetic_category", StringType(), True),
])


# Pydantic Models for Validation
class Patient(BaseModel):
    """Patient demographic information."""
    patient_id: str
    date_of_birth: Optional[date] = None
    gender: Optional[str] = None
    race: Optional[str] = None
    ethnicity: Optional[str] = None
    zip_code: Optional[str] = None
    insurance_type: Optional[str] = None
    enrollment_start_date: Optional[date] = None
    enrollment_end_date: Optional[date] = None

    @validator('patient_id')
    def validate_patient_id(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Patient ID cannot be empty')
        return v.strip()


class MedicalClaim(BaseModel):
    """Medical claim information."""
    claim_id: str
    patient_id: str
    service_date: date
    provider_id: Optional[str] = None
    provider_specialty: Optional[str] = None
    icd10_primary: Optional[str] = None
    icd10_secondary: Optional[List[str]] = []
    cpt_codes: Optional[List[str]] = []
    claim_amount: Optional[float] = None
    paid_amount: Optional[float] = None
    place_of_service: Optional[str] = None
    claim_type: Optional[ClaimType] = None

    @validator('claim_amount', 'paid_amount')
    def validate_amounts(cls, v):
        if v is not None and v < 0:
            raise ValueError('Claim amounts cannot be negative')
        return v


class PharmacyClaim(BaseModel):
    """Pharmacy claim information."""
    claim_id: str
    patient_id: str
    fill_date: date
    ndc_code: str
    drug_name: Optional[str] = None
    generic_name: Optional[str] = None
    therapeutic_class: Optional[str] = None
    days_supply: Optional[int] = None
    quantity: Optional[float] = None
    pharmacy_id: Optional[str] = None
    prescriber_id: Optional[str] = None
    claim_amount: Optional[float] = None
    paid_amount: Optional[float] = None

    @validator('days_supply')
    def validate_days_supply(cls, v):
        if v is not None and v <= 0:
            raise ValueError('Days supply must be positive')
        return v


class PatientProfile(BaseModel):
    """Patient profile with claims patterns."""
    patient_id: str
    has_genetic_diagnosis: bool
    genetic_icd_codes: List[str] = []
    medical_claim_count: int = 0
    pharmacy_claim_count: int = 0
    total_medical_cost: float = 0.0
    total_pharmacy_cost: float = 0.0
    common_icd_codes: List[str] = []
    common_drug_classes: List[str] = []
    provider_specialties: List[str] = []
    claim_frequency_score: float = 0.0
    complexity_score: float = 0.0
    profile_features: Dict[str, Any] = {}


class GeneticTestingRecommendation(BaseModel):
    """Genetic testing recommendation for a patient."""
    patient_id: str
    risk_score: float = Field(..., ge=0.0, le=1.0)
    risk_level: GeneticRiskLevel
    confidence: float = Field(..., ge=0.0, le=1.0)
    similar_patients: List[str] = []
    key_indicators: List[str] = []
    recommended_tests: List[str] = []
    reasoning: str
    created_at: datetime = Field(default_factory=datetime.now)

    @validator('risk_score', 'confidence')
    def validate_scores(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('Scores must be between 0.0 and 1.0')
        return v


# Genetic disorder ICD-10 code mappings
GENETIC_ICD10_CODES = {
    # Family history of genetic disorders (Z80-Z84)
    'Z80': 'Family history of malignant neoplasm',
    'Z81': 'Family history of mental and behavioral disorders',
    'Z82': 'Family history of certain disabilities and chronic diseases',
    'Z83': 'Family history of other specific disorders',
    'Z84': 'Family history of other conditions',
    
    # Specific genetic conditions
    'Q90': 'Down syndrome',
    'Q91': 'Edwards syndrome and Patau syndrome',
    'Q93': 'Monosomies and deletions from autosomes',
    'Q95': 'Balanced rearrangements and structural markers',
    'Q96': 'Turner syndrome',
    'Q97': 'Other sex chromosome abnormalities',
    'Q98': 'Other chromosome abnormalities',
    'Q99': 'Other chromosome abnormalities',
    'E70': 'Disorders of aromatic amino-acid metabolism',
    'E71': 'Disorders of branched-chain amino-acid metabolism',
    'E72': 'Other disorders of amino-acid metabolism',
    'E74': 'Other disorders of carbohydrate metabolism',
    'E75': 'Disorders of sphingolipid metabolism',
    'E76': 'Disorders of glycosaminoglycan metabolism',
    'E77': 'Disorders of glycoprotein metabolism',
    'E78': 'Disorders of lipoprotein metabolism',
    'E79': 'Disorders of purine and pyrimidine metabolism',
    'E80': 'Disorders of porphyrin and bilirubin metabolism',
}
