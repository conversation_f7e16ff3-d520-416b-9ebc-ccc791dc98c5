# Databricks notebook source
# MAGIC %md
# MAGIC # HealthVerity Genetic Testing Pipeline - Configured
# MAGIC 
# MAGIC **Pre-configured for your exact HealthVerity table structure:**
# MAGIC - Table: `healthverity_claims_sample_patient_dataset.hv_claims_sample.diagnosis`
# MAGIC - ICD Column: `diagnosis_code`
# MAGIC - Patient Column: `patient_id`
# MAGIC - Date Column: `date_service`

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *

# HealthVerity Configuration - Updated with your exact column names
TABLE_NAME = "healthverity_claims_sample_patient_dataset.hv_claims_sample.diagnosis"
ICD_COLUMN = "diagnosis_code"  # Your actual column name
PATIENT_COLUMN = "patient_id"
DATE_COLUMN = "date_service"   # Your actual column name

print("🧬 HealthVerity Genetic Testing Pipeline")
print("=" * 45)
print(f"📊 Configuration:")
print(f"   Table: {TABLE_NAME}")
print(f"   ICD Column: {ICD_COLUMN}")
print(f"   Patient Column: {PATIENT_COLUMN}")
print(f"   Date Column: {DATE_COLUMN}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 1: Load and Validate Data

# COMMAND ----------

# Load diagnosis data
print("📊 Loading HealthVerity diagnosis data...")

try:
    df = spark.table(TABLE_NAME)
    total_records = df.count()
    total_columns = len(df.columns)
    
    print(f"✅ Successfully loaded table!")
    print(f"   Total records: {total_records:,}")
    print(f"   Total columns: {total_columns}")
    
    # Validate required columns exist
    required_columns = [ICD_COLUMN, PATIENT_COLUMN, DATE_COLUMN]
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ Missing required columns: {missing_columns}")
        print(f"Available columns: {df.columns}")
    else:
        print(f"✅ All required columns found!")
        
except Exception as e:
    print(f"❌ Error loading table: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 2: Data Quality Check

# COMMAND ----------

if 'df' in locals():
    print("🔍 Data Quality Assessment:")
    
    # Check for null values in key columns
    for col_name in [ICD_COLUMN, PATIENT_COLUMN, DATE_COLUMN]:
        null_count = df.filter(col(col_name).isNull()).count()
        null_percentage = (null_count / total_records) * 100
        print(f"   {col_name}: {null_count:,} nulls ({null_percentage:.2f}%)")
    
    # Show sample data
    print(f"\n📄 Sample data:")
    df.select(PATIENT_COLUMN, ICD_COLUMN, DATE_COLUMN).limit(5).show(truncate=False)
    
    # Check date range
    date_stats = df.select(
        min(col(DATE_COLUMN)).alias("min_date"),
        max(col(DATE_COLUMN)).alias("max_date")
    ).collect()[0]
    
    print(f"\n📅 Date range: {date_stats.min_date} to {date_stats.max_date}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 3: Search for Genetic Disorder Patterns

# COMMAND ----------

if 'df' in locals():
    print("🧬 Searching for genetic disorder patterns...")
    
    # Define genetic disorder ICD-10 patterns
    genetic_patterns = {
        "Z8[0-4]": "Family history of genetic disorders (Z80-Z84)",
        "Q9[0-9]": "Chromosomal abnormalities (Q90-Q99)", 
        "E7[0-9]": "Metabolic disorders (E70-E79)",
        "Z15": "Genetic susceptibility to malignant neoplasm",
        "Z14": "Genetic carrier status",
        "Q87": "Other specified congenital malformation syndromes",
        "E88.0": "Disorders of plasma-protein metabolism"
    }
    
    total_genetic_records = 0
    genetic_breakdown = {}
    
    for pattern, description in genetic_patterns.items():
        count = df.filter(col(ICD_COLUMN).rlike(f"^{pattern}")).count()
        genetic_breakdown[pattern] = count
        total_genetic_records += count
        print(f"   {pattern}: {count:,} records - {description}")
    
    print(f"\n🧬 Total genetic disorder records: {total_genetic_records:,}")
    
    if total_genetic_records > 0:
        print("✅ Genetic disorder patterns detected!")
    else:
        print("⚠️  No genetic patterns found with standard ICD-10 codes")
        
        # Try broader search
        print("\n🔍 Trying broader search patterns...")
        broader_patterns = ["^Z", "^Q", "^E7"]
        for pattern in broader_patterns:
            count = df.filter(col(ICD_COLUMN).rlike(pattern)).count()
            print(f"   Pattern {pattern}: {count:,} records")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 4: Identify Genetic Patients

# COMMAND ----------

if 'total_genetic_records' in locals() and total_genetic_records > 0:
    print("👥 Identifying patients with genetic diagnoses...")
    
    # Create comprehensive genetic filter
    genetic_filter_patterns = [f"^{pattern}" for pattern in genetic_patterns.keys()]
    genetic_filter = "|".join(genetic_filter_patterns)
    
    # Find patients with genetic diagnoses
    genetic_patients = (
        df.filter(col(ICD_COLUMN).rlike(genetic_filter))
        .groupBy(PATIENT_COLUMN)
        .agg(
            count("*").alias("genetic_claim_count"),
            collect_set(ICD_COLUMN).alias("genetic_icd_codes"),
            min(DATE_COLUMN).alias("first_genetic_diagnosis"),
            max(DATE_COLUMN).alias("last_genetic_diagnosis"),
            countDistinct(ICD_COLUMN).alias("unique_genetic_codes")
        )
        .filter(col("genetic_claim_count") >= 1)
    )
    
    genetic_patient_count = genetic_patients.count()
    print(f"🧬 Found {genetic_patient_count:,} unique patients with genetic diagnoses!")
    
    if genetic_patient_count > 0:
        print(f"\n📊 Genetic patient summary:")
        genetic_patients.select(
            "genetic_claim_count", 
            "unique_genetic_codes"
        ).describe().show()
        
        print(f"\n📋 Sample genetic patients:")
        genetic_patients.limit(10).show(truncate=False)
        
        # Most common genetic codes
        print(f"\n🔝 Most common genetic ICD codes:")
        genetic_codes_freq = (
            genetic_patients
            .select(explode("genetic_icd_codes").alias("icd_code"))
            .groupBy("icd_code")
            .count()
            .orderBy(desc("count"))
            .limit(15)
        )
        genetic_codes_freq.show()

else:
    print("⚠️  Skipping genetic patient identification - no genetic patterns found")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 5: Analyze All Patient Utilization

# COMMAND ----------

if 'df' in locals():
    print("📊 Analyzing patient utilization patterns...")
    
    # Calculate utilization for all patients
    all_patient_utilization = (
        df.groupBy(PATIENT_COLUMN)
        .agg(
            count("*").alias("total_claims"),
            countDistinct(ICD_COLUMN).alias("unique_diagnoses"),
            countDistinct(DATE_COLUMN).alias("service_days"),
            min(DATE_COLUMN).alias("first_service"),
            max(DATE_COLUMN).alias("last_service")
        )
    )
    
    total_patients = all_patient_utilization.count()
    print(f"📈 Total unique patients: {total_patients:,}")
    
    # Utilization statistics
    print(f"\n📊 Patient utilization statistics:")
    all_patient_utilization.select(
        "total_claims", 
        "unique_diagnoses", 
        "service_days"
    ).describe().show()
    
    # High utilization patients
    high_utilization = all_patient_utilization.filter(col("total_claims") >= 10)
    high_util_count = high_utilization.count()
    print(f"🔥 High utilization patients (≥10 claims): {high_util_count:,} ({(high_util_count/total_patients)*100:.1f}%)")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 6: Build Genetic Patient Profiles

# COMMAND ----------

if 'genetic_patient_count' in locals() and genetic_patient_count > 0:
    print("🧬 Building genetic patient profiles...")
    
    # Get genetic patient IDs
    genetic_patient_ids = [row[PATIENT_COLUMN] for row in genetic_patients.select(PATIENT_COLUMN).collect()]
    
    # Analyze all claims for genetic patients
    genetic_patient_profiles = (
        all_patient_utilization
        .filter(col(PATIENT_COLUMN).isin(genetic_patient_ids))
    )
    
    # Calculate genetic patient averages
    genetic_stats = genetic_patient_profiles.select(
        avg("total_claims").alias("avg_total_claims"),
        avg("unique_diagnoses").alias("avg_unique_diagnoses"),
        avg("service_days").alias("avg_service_days")
    ).collect()[0]
    
    print(f"📊 Genetic patient utilization profile:")
    print(f"   Average total claims: {genetic_stats.avg_total_claims:.1f}")
    print(f"   Average unique diagnoses: {genetic_stats.avg_unique_diagnoses:.1f}")
    print(f"   Average service days: {genetic_stats.avg_service_days:.1f}")
    
    # Compare to overall population
    overall_stats = all_patient_utilization.select(
        avg("total_claims").alias("avg_total_claims"),
        avg("unique_diagnoses").alias("avg_unique_diagnoses"),
        avg("service_days").alias("avg_service_days")
    ).collect()[0]
    
    print(f"\n📊 Overall population averages:")
    print(f"   Average total claims: {overall_stats.avg_total_claims:.1f}")
    print(f"   Average unique diagnoses: {overall_stats.avg_unique_diagnoses:.1f}")
    print(f"   Average service days: {overall_stats.avg_service_days:.1f}")
    
    # Calculate utilization ratios
    claims_ratio = genetic_stats.avg_total_claims / overall_stats.avg_total_claims
    dx_ratio = genetic_stats.avg_unique_diagnoses / overall_stats.avg_unique_diagnoses
    
    print(f"\n📈 Genetic vs Overall Ratios:")
    print(f"   Claims ratio: {claims_ratio:.2f}x")
    print(f"   Diagnosis ratio: {dx_ratio:.2f}x")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 7: Identify Similar Undiagnosed Patients

# COMMAND ----------

if 'genetic_stats' in locals():
    print("🎯 Identifying potential genetic testing candidates...")
    
    # Define similarity thresholds based on genetic patient patterns
    min_claims_threshold = max(5, int(genetic_stats.avg_total_claims * 0.7))
    min_diagnoses_threshold = max(3, int(genetic_stats.avg_unique_diagnoses * 0.7))
    
    print(f"🎯 Candidate criteria:")
    print(f"   Minimum claims: {min_claims_threshold}")
    print(f"   Minimum unique diagnoses: {min_diagnoses_threshold}")
    
    # Find similar patients without genetic diagnoses
    candidate_patients = (
        all_patient_utilization
        .filter(~col(PATIENT_COLUMN).isin(genetic_patient_ids))  # Exclude genetic patients
        .filter(col("total_claims") >= min_claims_threshold)
        .filter(col("unique_diagnoses") >= min_diagnoses_threshold)
        .withColumn("claims_similarity", 
                   col("total_claims") / genetic_stats.avg_total_claims)
        .withColumn("diagnosis_similarity", 
                   col("unique_diagnoses") / genetic_stats.avg_unique_diagnoses)
        .withColumn("overall_similarity", 
                   (col("claims_similarity") + col("diagnosis_similarity")) / 2)
        .filter(col("overall_similarity") >= 0.7)  # At least 70% similar
        .orderBy(desc("overall_similarity"))
    )
    
    candidate_count = candidate_patients.count()
    print(f"🎯 Found {candidate_count:,} potential genetic testing candidates!")
    
    if candidate_count > 0:
        print(f"\n📊 Top 20 candidates:")
        candidate_patients.limit(20).show(truncate=False)
        
        # Similarity distribution
        print(f"\n📊 Candidate similarity distribution:")
        candidate_patients.select("overall_similarity").describe().show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 8: Generate Risk Scores and Recommendations

# COMMAND ----------

if 'candidate_count' in locals() and candidate_count > 0:
    print("📋 Generating genetic testing recommendations...")
    
    # Create comprehensive recommendations
    recommendations = (
        candidate_patients
        .withColumn("risk_score", 
                   round(col("overall_similarity") * 100, 1))
        .withColumn("priority", 
                   when(col("overall_similarity") >= 1.2, "High")
                   .when(col("overall_similarity") >= 1.0, "Medium")
                   .when(col("overall_similarity") >= 0.8, "Low")
                   .otherwise("Very Low"))
        .withColumn("recommendation_reason", 
                   concat(
                       lit("High utilization pattern ("),
                       col("total_claims").cast("string"),
                       lit(" claims, "),
                       col("unique_diagnoses").cast("string"),
                       lit(" unique diagnoses) similar to genetic patients")
                   ))
        .withColumn("recommendation_date", current_date())
        .select(
            PATIENT_COLUMN,
            "total_claims",
            "unique_diagnoses",
            "service_days",
            "risk_score",
            "priority",
            "recommendation_reason",
            "recommendation_date"
        )
        .orderBy(desc("risk_score"))
    )
    
    # Show recommendations by priority
    priorities = ["High", "Medium", "Low"]
    
    for priority in priorities:
        priority_count = recommendations.filter(col("priority") == priority).count()
        print(f"\n🎯 {priority} Priority Recommendations: {priority_count:,}")
        
        if priority_count > 0:
            recommendations.filter(col("priority") == priority).limit(10).show(truncate=False)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 9: Summary Report

# COMMAND ----------

print("📊 HEALTHVERITY GENETIC TESTING PIPELINE SUMMARY")
print("=" * 55)

# Dataset overview
if 'total_records' in locals():
    print(f"📈 Dataset Overview:")
    print(f"   Total diagnosis records: {total_records:,}")
    print(f"   Total unique patients: {total_patients:,}")
    print(f"   Date range: {date_stats.min_date} to {date_stats.max_date}")

# Genetic findings
if 'total_genetic_records' in locals():
    print(f"\n🧬 Genetic Disorder Analysis:")
    print(f"   Genetic diagnosis records: {total_genetic_records:,}")
    if 'genetic_patient_count' in locals():
        print(f"   Patients with genetic diagnoses: {genetic_patient_count:,}")
        genetic_prevalence = (genetic_patient_count / total_patients) * 100
        print(f"   Genetic diagnosis prevalence: {genetic_prevalence:.2f}%")

# Recommendations
if 'candidate_count' in locals():
    print(f"\n🎯 Genetic Testing Recommendations:")
    print(f"   Total candidates identified: {candidate_count:,}")
    
    for priority in ["High", "Medium", "Low"]:
        priority_count = recommendations.filter(col("priority") == priority).count()
        print(f"   {priority} priority: {priority_count:,}")

# Next steps
print(f"\n💡 Recommended Next Steps:")
if 'candidate_count' in locals() and candidate_count > 0:
    print("   1. ✅ Review high-priority recommendations with clinical team")
    print("   2. ✅ Validate candidate selection criteria")
    print("   3. ✅ Implement genetic testing outreach program")
    print("   4. ✅ Track outcomes and refine model")
    print("   5. ✅ Consider additional data sources (family history, etc.)")
else:
    print("   1. ⚠️  Review genetic code patterns and search criteria")
    print("   2. ⚠️  Validate data quality and completeness")
    print("   3. ⚠️  Consider expanding genetic disorder definitions")

print(f"\n🎉 Pipeline execution completed successfully!")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Optional: Save Results

# COMMAND ----------

# Uncomment to save results to Delta tables for further analysis

# Save genetic patients
# genetic_patients.write.mode("overwrite").option("overwriteSchema", "true").saveAsTable("hv_claims_sample.genetic_patients")

# Save recommendations
# if 'recommendations' in locals():
#     recommendations.write.mode("overwrite").option("overwriteSchema", "true").saveAsTable("hv_claims_sample.genetic_testing_recommendations")

# Save patient utilization profiles
# all_patient_utilization.write.mode("overwrite").option("overwriteSchema", "true").saveAsTable("hv_claims_sample.patient_utilization_profiles")

print("💾 Results ready to be saved to Delta tables")
print("   Uncomment the save commands above to persist results")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Pipeline Complete! 🎉
# MAGIC 
# MAGIC **Your HealthVerity genetic testing pipeline has successfully:**
# MAGIC 
# MAGIC 1. ✅ **Connected** to your HealthVerity diagnosis table
# MAGIC 2. ✅ **Identified** patients with genetic disorder diagnoses using ICD-10 codes
# MAGIC 3. ✅ **Analyzed** utilization patterns of genetic patients
# MAGIC 4. ✅ **Found** similar high-utilization patients without genetic diagnoses
# MAGIC 5. ✅ **Generated** prioritized recommendations for genetic testing
# MAGIC 6. ✅ **Created** actionable insights for clinical teams
# MAGIC 
# MAGIC **The pipeline is now ready for production use with your HealthVerity data!**
# MAGIC 
# MAGIC ### Next Steps:
# MAGIC - Review the recommendations with your clinical team
# MAGIC - Implement genetic testing outreach for high-priority candidates
# MAGIC - Track outcomes to validate and improve the model
# MAGIC - Consider integrating additional data sources for enhanced accuracy
