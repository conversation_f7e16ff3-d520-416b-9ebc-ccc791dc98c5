"""
Setup script for HealthVerity Databricks environment.
Run this script to configure and test the genetic testing pipeline with your HealthVerity data.
"""

import os
import sys
from pyspark.sql import SparkSession

# Add source path
sys.path.append('./src')

def setup_environment():
    """Set up the HealthVerity Databricks environment."""
    
    print("🧬 Setting up Genetic Testing Pipeline for HealthVerity")
    print("=" * 60)
    
    # Create environment variables
    env_vars = {
        'DATABRICKS_HOST': 'adb-3961822481655844.4.azuredatabricks.net',
        'DATABRICKS_HTTP_PATH': '/sql/1.0/warehouses/3b64ec91bab74a2f',
        'CATALOG_NAME': 'healthverity_claims_sample_patient_dataset',
        'DATABASE_NAME': 'hv_claims_sample',
        'DIAGNOSIS_TABLE': 'diagnosis',
        'LOOKBACK_DAYS': '365',
        'MIN_CLAIMS_THRESHOLD': '3',
        'SIMILARITY_THRESHOLD': '0.7',
        'RISK_THRESHOLD': '0.5'
    }
    
    print("📝 Setting environment variables...")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"   {key} = {value}")
    
    print("\n✅ Environment variables set successfully!")
    
    return env_vars

def test_databricks_connection():
    """Test connection to Databricks and explore the diagnosis table."""
    
    print("\n🔗 Testing Databricks connection...")
    
    try:
        # Initialize Spark session
        spark = SparkSession.builder \
            .appName("GeneticTestingHealthVerity") \
            .getOrCreate()
        
        print("✅ Spark session created successfully!")
        
        # Test table access
        table_name = "healthverity_claims_sample_patient_dataset.hv_claims_sample.diagnosis"
        print(f"\n📊 Testing access to table: {table_name}")
        
        try:
            df = spark.table(table_name)
            row_count = df.count()
            column_count = len(df.columns)
            
            print(f"✅ Table access successful!")
            print(f"   Rows: {row_count:,}")
            print(f"   Columns: {column_count}")
            
            # Show schema
            print(f"\n📋 Table Schema:")
            df.printSchema()
            
            # Show sample data
            print(f"\n📄 Sample Data (first 5 rows):")
            df.limit(5).show(truncate=False)
            
            return True, df
            
        except Exception as e:
            print(f"❌ Failed to access table: {str(e)}")
            return False, None
            
    except Exception as e:
        print(f"❌ Failed to create Spark session: {str(e)}")
        return False, None

def explore_genetic_patterns(df):
    """Explore genetic disorder patterns in the data."""
    
    print("\n🧬 Exploring genetic disorder patterns...")
    
    from pyspark.sql.functions import col, count, desc
    
    # Look for columns that might contain ICD codes
    potential_icd_columns = []
    for col_name in df.columns:
        if any(keyword in col_name.lower() for keyword in ['icd', 'diagnosis', 'code', 'dx']):
            potential_icd_columns.append(col_name)
    
    print(f"📋 Potential ICD code columns found: {potential_icd_columns}")
    
    if not potential_icd_columns:
        print("⚠️  No obvious ICD code columns found. Manual inspection needed.")
        return
    
    # Check each potential column for genetic patterns
    genetic_patterns = ["Z8[0-4].*", "Q9[0-9].*", "E7[0-9].*"]
    
    for col_name in potential_icd_columns:
        print(f"\n🔍 Analyzing column: {col_name}")
        
        try:
            # Show sample values
            sample_values = df.select(col_name).filter(col(col_name).isNotNull()).limit(10)
            print("   Sample values:")
            sample_values.show(truncate=False)
            
            # Check for genetic patterns
            for pattern in genetic_patterns:
                try:
                    pattern_count = df.filter(col(col_name).rlike(pattern)).count()
                    print(f"   Pattern {pattern}: {pattern_count:,} matches")
                except:
                    print(f"   Pattern {pattern}: Error checking pattern")
                    
        except Exception as e:
            print(f"   Error analyzing column: {str(e)}")

def setup_genetic_testing_pipeline():
    """Set up and test the genetic testing pipeline components."""
    
    print("\n🔧 Setting up genetic testing pipeline components...")
    
    try:
        from data.healthverity_loader import HealthVerityDataLoader
        from models.icd_classifier import ICD10GeneticClassifier
        
        # Initialize Spark
        spark = SparkSession.builder \
            .appName("GeneticTestingHealthVerity") \
            .getOrCreate()
        
        # Initialize components
        loader = HealthVerityDataLoader(spark)
        classifier = ICD10GeneticClassifier(spark)
        
        print("✅ Pipeline components initialized successfully!")
        
        # Test data loading
        print("\n📊 Testing data loading...")
        diagnosis_df = loader.load_diagnosis_data(limit=1000)  # Load sample
        print(f"✅ Loaded {diagnosis_df.count()} diagnosis records")
        
        # Test genetic patient identification
        print("\n🧬 Testing genetic patient identification...")
        genetic_patients = loader.identify_genetic_patients_healthverity(min_genetic_claims=1)
        genetic_count = genetic_patients.count()
        
        if genetic_count > 0:
            print(f"✅ Found {genetic_count} patients with genetic diagnoses!")
            
            # Show sample genetic patients
            print("\n📋 Sample genetic patients:")
            genetic_patients.limit(5).show(truncate=False)
            
        else:
            print("⚠️  No genetic patients found. May need to adjust search patterns.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up pipeline: {str(e)}")
        return False

def generate_next_steps():
    """Generate next steps for the user."""
    
    print("\n🎯 Next Steps:")
    print("=" * 40)
    
    print("1. 📊 Run the data exploration notebook:")
    print("   - Open: notebooks/healthverity_data_exploration.py")
    print("   - This will help identify the correct column mappings")
    
    print("\n2. 🔧 Update column mappings:")
    print("   - Based on exploration results, update column mappings in:")
    print("   - src/data/healthverity_loader.py")
    
    print("\n3. 🧬 Run genetic patient identification:")
    print("   - Use the HealthVerityDataLoader to identify genetic patients")
    print("   - Adjust genetic code patterns if needed")
    
    print("\n4. 🤖 Develop ML models:")
    print("   - Build patient profiles")
    print("   - Train similarity matching models")
    print("   - Create risk scoring models")
    
    print("\n5. 📈 Generate recommendations:")
    print("   - Run the complete pipeline")
    print("   - Generate genetic testing recommendations")
    
    print("\n📚 Documentation:")
    print("   - API Reference: docs/API_REFERENCE.md")
    print("   - Deployment Guide: docs/DEPLOYMENT_GUIDE.md")

def main():
    """Main setup function."""
    
    print("🧬 HealthVerity Genetic Testing Pipeline Setup")
    print("=" * 50)
    
    # Step 1: Setup environment
    env_vars = setup_environment()
    
    # Step 2: Test Databricks connection
    connection_success, df = test_databricks_connection()
    
    if not connection_success:
        print("\n❌ Setup failed at Databricks connection step.")
        print("Please check your Databricks credentials and table access.")
        return
    
    # Step 3: Explore genetic patterns
    if df is not None:
        explore_genetic_patterns(df)
    
    # Step 4: Setup pipeline components
    pipeline_success = setup_genetic_testing_pipeline()
    
    # Step 5: Generate next steps
    generate_next_steps()
    
    if connection_success and pipeline_success:
        print("\n🎉 Setup completed successfully!")
        print("You're ready to start identifying genetic testing candidates!")
    else:
        print("\n⚠️  Setup completed with some issues.")
        print("Please review the errors above and follow the next steps.")

if __name__ == "__main__":
    main()
