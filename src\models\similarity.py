"""
Similarity matching algorithm for identifying undiagnosed patients.
Uses ML techniques to find patients with similar patterns to genetic disorder patients.
"""

import logging
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from sklearn.neighbors import NearestNeighbors
from sklearn.metrics.pairwise import cosine_similarity, euclidean_distances
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import pandas as pd

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import (
    col, when, lit, array, struct, collect_list,
    broadcast, desc, asc, row_number, rank
)
from pyspark.sql.window import Window
from pyspark.ml.feature import VectorAssembler, StandardScaler as SparkStandardScaler
from pyspark.ml.linalg import Vectors, VectorUDT
from pyspark.sql.types import ArrayType, DoubleType

from ..utils.config import get_config

logger = logging.getLogger(__name__)


class SimilarityMatcher:
    """Matches undiagnosed patients to genetic disorder patients based on similarity."""
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = get_config()
        self.scaler = StandardScaler()
        self.similarity_threshold = self.config.settings.similarity_threshold
        
    def find_similar_undiagnosed_patients(self,
                                        genetic_profiles_df: DataFrame,
                                        all_patient_features_df: DataFrame,
                                        similarity_method: str = "cosine") -> DataFrame:
        """
        Find undiagnosed patients similar to genetic disorder patients.
        
        Args:
            genetic_profiles_df: DataFrame with genetic patient profiles
            all_patient_features_df: DataFrame with all patient features
            similarity_method: Similarity calculation method
            
        Returns:
            DataFrame with similar undiagnosed patients and their similarity scores
        """
        logger.info("Finding similar undiagnosed patients")
        
        # Get genetic patient IDs
        genetic_patient_ids = [row.patient_id for row in 
                              genetic_profiles_df.select("patient_id").collect()]
        
        # Filter out genetic patients from all patients
        undiagnosed_patients_df = all_patient_features_df.filter(
            ~col("patient_id").isin(genetic_patient_ids)
        )
        
        logger.info(f"Analyzing {undiagnosed_patients_df.count()} undiagnosed patients")
        
        # Prepare feature vectors
        genetic_features = self._prepare_feature_vectors(genetic_profiles_df)
        undiagnosed_features = self._prepare_feature_vectors(undiagnosed_patients_df)
        
        # Calculate similarities
        if similarity_method == "cosine":
            similarities_df = self._calculate_cosine_similarities(
                genetic_features, undiagnosed_features
            )
        elif similarity_method == "euclidean":
            similarities_df = self._calculate_euclidean_similarities(
                genetic_features, undiagnosed_features
            )
        elif similarity_method == "knn":
            similarities_df = self._calculate_knn_similarities(
                genetic_features, undiagnosed_features
            )
        else:
            raise ValueError(f"Unknown similarity method: {similarity_method}")
        
        # Filter by similarity threshold and rank
        similar_patients = self._filter_and_rank_similarities(similarities_df)
        
        logger.info(f"Found {similar_patients.count()} similar undiagnosed patients")
        
        return similar_patients
    
    def _prepare_feature_vectors(self, df: DataFrame) -> DataFrame:
        """Prepare feature vectors for similarity calculation."""
        
        # Select numerical features for similarity calculation
        feature_cols = [
            "claim_frequency_score", "cost_intensity_score",
            "provider_diversity_score", "medication_complexity_score",
            "care_coordination_score", "medical_claim_count",
            "pharmacy_claim_count", "total_medical_cost",
            "total_pharmacy_cost", "unique_providers",
            "unique_specialties", "unique_medications",
            "specialist_visit_ratio", "chronic_medication_ratio",
            "avg_complexity_score", "avg_adherence_ratio"
        ]
        
        # Fill missing values with 0
        df_filled = df
        for col_name in feature_cols:
            df_filled = df_filled.withColumn(col_name, 
                                           when(col(col_name).isNull(), 0.0)
                                           .otherwise(col(col_name)))
        
        # Assemble feature vector
        assembler = VectorAssembler(inputCols=feature_cols, outputCol="features")
        df_features = assembler.transform(df_filled)
        
        # Scale features
        scaler = SparkStandardScaler(inputCol="features", outputCol="scaled_features")
        scaler_model = scaler.fit(df_features)
        df_scaled = scaler_model.transform(df_features)
        
        return df_scaled.select("patient_id", "scaled_features")
    
    def _calculate_cosine_similarities(self,
                                     genetic_features: DataFrame,
                                     undiagnosed_features: DataFrame) -> DataFrame:
        """Calculate cosine similarities between genetic and undiagnosed patients."""
        
        # Convert to Pandas for similarity calculation
        genetic_pandas = genetic_features.toPandas()
        undiagnosed_pandas = undiagnosed_features.toPandas()
        
        # Extract feature vectors
        genetic_vectors = np.array([
            np.array(row.scaled_features.toArray()) 
            for row in genetic_pandas.itertuples()
        ])
        
        undiagnosed_vectors = np.array([
            np.array(row.scaled_features.toArray()) 
            for row in undiagnosed_pandas.itertuples()
        ])
        
        # Calculate cosine similarity matrix
        similarity_matrix = cosine_similarity(undiagnosed_vectors, genetic_vectors)
        
        # Create similarity DataFrame
        similarity_data = []
        for i, undiagnosed_id in enumerate(undiagnosed_pandas['patient_id']):
            similarities = similarity_matrix[i]
            max_similarity = np.max(similarities)
            best_match_idx = np.argmax(similarities)
            best_match_id = genetic_pandas.iloc[best_match_idx]['patient_id']
            
            # Get top 5 similar genetic patients
            top_indices = np.argsort(similarities)[::-1][:5]
            top_genetic_patients = [genetic_pandas.iloc[idx]['patient_id'] for idx in top_indices]
            top_similarities = [float(similarities[idx]) for idx in top_indices]
            
            similarity_data.append({
                'undiagnosed_patient_id': undiagnosed_id,
                'max_similarity_score': float(max_similarity),
                'best_match_genetic_patient': best_match_id,
                'top_similar_genetic_patients': top_genetic_patients,
                'top_similarity_scores': top_similarities,
                'avg_similarity': float(np.mean(similarities))
            })
        
        return self.spark.createDataFrame(similarity_data)
    
    def _calculate_euclidean_similarities(self,
                                        genetic_features: DataFrame,
                                        undiagnosed_features: DataFrame) -> DataFrame:
        """Calculate Euclidean distance-based similarities."""
        
        # Convert to Pandas
        genetic_pandas = genetic_features.toPandas()
        undiagnosed_pandas = undiagnosed_features.toPandas()
        
        # Extract feature vectors
        genetic_vectors = np.array([
            np.array(row.scaled_features.toArray()) 
            for row in genetic_pandas.itertuples()
        ])
        
        undiagnosed_vectors = np.array([
            np.array(row.scaled_features.toArray()) 
            for row in undiagnosed_pandas.itertuples()
        ])
        
        # Calculate Euclidean distances
        distance_matrix = euclidean_distances(undiagnosed_vectors, genetic_vectors)
        
        # Convert distances to similarities (smaller distance = higher similarity)
        max_distance = np.max(distance_matrix)
        similarity_matrix = 1 - (distance_matrix / max_distance)
        
        # Create similarity DataFrame
        similarity_data = []
        for i, undiagnosed_id in enumerate(undiagnosed_pandas['patient_id']):
            similarities = similarity_matrix[i]
            max_similarity = np.max(similarities)
            best_match_idx = np.argmax(similarities)
            best_match_id = genetic_pandas.iloc[best_match_idx]['patient_id']
            
            # Get top 5 similar genetic patients
            top_indices = np.argsort(similarities)[::-1][:5]
            top_genetic_patients = [genetic_pandas.iloc[idx]['patient_id'] for idx in top_indices]
            top_similarities = [float(similarities[idx]) for idx in top_indices]
            
            similarity_data.append({
                'undiagnosed_patient_id': undiagnosed_id,
                'max_similarity_score': float(max_similarity),
                'best_match_genetic_patient': best_match_id,
                'top_similar_genetic_patients': top_genetic_patients,
                'top_similarity_scores': top_similarities,
                'avg_similarity': float(np.mean(similarities))
            })
        
        return self.spark.createDataFrame(similarity_data)
    
    def _calculate_knn_similarities(self,
                                  genetic_features: DataFrame,
                                  undiagnosed_features: DataFrame) -> DataFrame:
        """Calculate similarities using K-Nearest Neighbors approach."""
        
        # Convert to Pandas
        genetic_pandas = genetic_features.toPandas()
        undiagnosed_pandas = undiagnosed_features.toPandas()
        
        # Extract feature vectors
        genetic_vectors = np.array([
            np.array(row.scaled_features.toArray()) 
            for row in genetic_pandas.itertuples()
        ])
        
        undiagnosed_vectors = np.array([
            np.array(row.scaled_features.toArray()) 
            for row in undiagnosed_pandas.itertuples()
        ])
        
        # Fit KNN model on genetic patients
        n_neighbors = min(10, len(genetic_vectors))
        knn = NearestNeighbors(n_neighbors=n_neighbors, metric='cosine')
        knn.fit(genetic_vectors)
        
        # Find nearest neighbors for undiagnosed patients
        distances, indices = knn.kneighbors(undiagnosed_vectors)
        
        # Create similarity DataFrame
        similarity_data = []
        for i, undiagnosed_id in enumerate(undiagnosed_pandas['patient_id']):
            neighbor_distances = distances[i]
            neighbor_indices = indices[i]
            
            # Convert distances to similarities
            similarities = 1 - neighbor_distances
            max_similarity = np.max(similarities)
            best_match_idx = neighbor_indices[0]
            best_match_id = genetic_pandas.iloc[best_match_idx]['patient_id']
            
            # Get top neighbors
            top_genetic_patients = [genetic_pandas.iloc[idx]['patient_id'] for idx in neighbor_indices]
            top_similarities = [float(sim) for sim in similarities]
            
            similarity_data.append({
                'undiagnosed_patient_id': undiagnosed_id,
                'max_similarity_score': float(max_similarity),
                'best_match_genetic_patient': best_match_id,
                'top_similar_genetic_patients': top_genetic_patients,
                'top_similarity_scores': top_similarities,
                'avg_similarity': float(np.mean(similarities))
            })
        
        return self.spark.createDataFrame(similarity_data)
    
    def _filter_and_rank_similarities(self, similarities_df: DataFrame) -> DataFrame:
        """Filter similarities by threshold and rank results."""
        
        # Filter by similarity threshold
        filtered_df = similarities_df.filter(
            col("max_similarity_score") >= self.similarity_threshold
        )
        
        # Add ranking
        window_spec = Window.orderBy(desc("max_similarity_score"))
        ranked_df = filtered_df.withColumn("similarity_rank", 
                                          row_number().over(window_spec))
        
        # Add similarity category
        ranked_df = ranked_df.withColumn("similarity_category",
                                        when(col("max_similarity_score") >= 0.8, "very_high")
                                        .when(col("max_similarity_score") >= 0.7, "high")
                                        .when(col("max_similarity_score") >= 0.6, "moderate")
                                        .otherwise("low"))
        
        return ranked_df
    
    def cluster_similar_patients(self, 
                                similarities_df: DataFrame,
                                n_clusters: int = 5) -> DataFrame:
        """
        Cluster similar patients to identify distinct patterns.
        
        Args:
            similarities_df: DataFrame with similarity scores
            n_clusters: Number of clusters to create
            
        Returns:
            DataFrame with cluster assignments
        """
        logger.info(f"Clustering similar patients into {n_clusters} clusters")
        
        # Convert similarity scores to feature vectors for clustering
        feature_data = similarities_df.select(
            "undiagnosed_patient_id",
            "max_similarity_score",
            "avg_similarity"
        ).toPandas()
        
        # Prepare features for clustering
        features = feature_data[['max_similarity_score', 'avg_similarity']].values
        
        # Perform K-means clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(features)
        
        # Add cluster labels to DataFrame
        feature_data['cluster'] = cluster_labels
        
        # Convert back to Spark DataFrame
        clustered_df = self.spark.createDataFrame(feature_data)
        
        # Join with original similarities DataFrame
        result_df = similarities_df.join(
            clustered_df.select("undiagnosed_patient_id", "cluster"),
            "undiagnosed_patient_id",
            "inner"
        )
        
        return result_df
    
    def validate_similarity_matching(self,
                                   known_genetic_patients: List[str],
                                   similarity_results: DataFrame) -> Dict[str, float]:
        """
        Validate similarity matching using known genetic patients.
        
        Args:
            known_genetic_patients: List of known genetic patient IDs
            similarity_results: DataFrame with similarity matching results
            
        Returns:
            Dictionary with validation metrics
        """
        logger.info("Validating similarity matching results")
        
        # Convert to Pandas for validation
        results_pandas = similarity_results.toPandas()
        
        # Check if any known genetic patients are in the undiagnosed results
        false_positives = 0
        total_results = len(results_pandas)
        
        for patient_id in results_pandas['undiagnosed_patient_id']:
            if patient_id in known_genetic_patients:
                false_positives += 1
        
        false_positive_rate = false_positives / total_results if total_results > 0 else 0.0
        
        # Calculate similarity distribution statistics
        similarity_scores = results_pandas['max_similarity_score']
        
        validation_metrics = {
            "false_positive_rate": false_positive_rate,
            "total_similar_patients": total_results,
            "avg_similarity_score": float(similarity_scores.mean()),
            "std_similarity_score": float(similarity_scores.std()),
            "min_similarity_score": float(similarity_scores.min()),
            "max_similarity_score": float(similarity_scores.max()),
            "similarity_score_percentiles": {
                "25th": float(similarity_scores.quantile(0.25)),
                "50th": float(similarity_scores.quantile(0.50)),
                "75th": float(similarity_scores.quantile(0.75)),
                "90th": float(similarity_scores.quantile(0.90))
            }
        }
        
        return validation_metrics
