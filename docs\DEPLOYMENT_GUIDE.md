# Genetic Testing ML Pipeline - Deployment Guide

This guide provides step-by-step instructions for deploying the genetic testing ML pipeline in a Databricks environment.

## Prerequisites

### System Requirements
- Databricks Runtime 13.0+ with ML support
- Python 3.9+
- Minimum cluster configuration: 8 cores, 32GB RAM
- Delta Lake enabled
- MLflow integration

### Access Requirements
- Databricks workspace access
- Database read/write permissions
- MLflow experiment access
- Cluster creation permissions

## Environment Setup

### 1. Databricks Workspace Configuration

#### Create Cluster
```bash
# Cluster configuration
Databricks Runtime: 13.3 LTS ML
Worker Type: i3.xlarge (4 cores, 30.5 GB RAM)
Driver Type: i3.xlarge (4 cores, 30.5 GB RAM)
Workers: 2-8 (auto-scaling enabled)

# Spark Configuration
spark.sql.adaptive.enabled true
spark.sql.adaptive.coalescePartitions.enabled true
spark.sql.adaptive.skewJoin.enabled true
spark.databricks.delta.preview.enabled true
```

#### Install Libraries
```bash
# Install via cluster libraries or notebook
%pip install -r /Workspace/genetic_testing/requirements.txt
```

### 2. Environment Variables

Set the following environment variables in Databricks:

```bash
# Databricks Configuration
DATABRICKS_HOST=https://your-workspace.cloud.databricks.com
DATABRICKS_TOKEN=your-access-token
DATABRICKS_CLUSTER_ID=your-cluster-id

# Database Configuration
DATABASE_NAME=genetic_testing
CATALOG_NAME=main

# Table Names
PATIENTS_TABLE=patients
MEDICAL_CLAIMS_TABLE=medical_claims
PHARMACY_CLAIMS_TABLE=pharmacy_claims
ICD10_CODES_TABLE=icd10_codes

# Processing Configuration
BATCH_SIZE=10000
LOOKBACK_DAYS=365
MIN_CLAIMS_THRESHOLD=3

# Model Configuration
SIMILARITY_THRESHOLD=0.7
RISK_THRESHOLD=0.5
```

### 3. Database Setup

#### Create Database and Tables
```sql
-- Create database
CREATE DATABASE IF NOT EXISTS genetic_testing;
USE genetic_testing;

-- Create patients table
CREATE TABLE patients (
    patient_id STRING NOT NULL,
    date_of_birth DATE,
    gender STRING,
    race STRING,
    ethnicity STRING,
    zip_code STRING,
    insurance_type STRING,
    enrollment_start_date DATE,
    enrollment_end_date DATE
) USING DELTA
PARTITIONED BY (enrollment_year INT);

-- Create medical claims table
CREATE TABLE medical_claims (
    claim_id STRING NOT NULL,
    patient_id STRING NOT NULL,
    service_date DATE NOT NULL,
    provider_id STRING,
    provider_specialty STRING,
    icd10_primary STRING,
    icd10_secondary ARRAY<STRING>,
    cpt_codes ARRAY<STRING>,
    claim_amount DOUBLE,
    paid_amount DOUBLE,
    place_of_service STRING,
    claim_type STRING
) USING DELTA
PARTITIONED BY (service_year INT, service_month INT);

-- Create pharmacy claims table
CREATE TABLE pharmacy_claims (
    claim_id STRING NOT NULL,
    patient_id STRING NOT NULL,
    fill_date DATE NOT NULL,
    ndc_code STRING NOT NULL,
    drug_name STRING,
    generic_name STRING,
    therapeutic_class STRING,
    days_supply INT,
    quantity DOUBLE,
    pharmacy_id STRING,
    prescriber_id STRING,
    claim_amount DOUBLE,
    paid_amount DOUBLE
) USING DELTA
PARTITIONED BY (fill_year INT, fill_month INT);

-- Create ICD-10 codes reference table
CREATE TABLE icd10_codes (
    icd10_code STRING NOT NULL,
    description STRING NOT NULL,
    category STRING,
    is_genetic_related BOOLEAN,
    genetic_category STRING
) USING DELTA;
```

## Data Loading

### 1. Load Reference Data

```python
# Load ICD-10 reference data
icd10_data = [
    ("Z80.0", "Family history of malignant neoplasm of digestive organs", "Family History", True, "family_history_malignant"),
    ("Z80.1", "Family history of malignant neoplasm of trachea, bronchus and lung", "Family History", True, "family_history_malignant"),
    ("Q90.0", "Trisomy 21, nonmosaicism (meiotic nondisjunction)", "Chromosomal", True, "chromosomal_abnormalities"),
    ("E70.0", "Classical phenylketonuria", "Metabolic", True, "metabolic_disorders"),
    # Add more ICD codes as needed
]

icd10_df = spark.createDataFrame(icd10_data, ["icd10_code", "description", "category", "is_genetic_related", "genetic_category"])
icd10_df.write.mode("overwrite").saveAsTable("genetic_testing.icd10_codes")
```

### 2. Load Claims Data

```python
# Example data loading from external sources
def load_claims_data():
    # Load from your data source (S3, ADLS, etc.)
    medical_claims_df = spark.read.format("delta").load("s3://your-bucket/medical_claims/")
    pharmacy_claims_df = spark.read.format("delta").load("s3://your-bucket/pharmacy_claims/")
    patients_df = spark.read.format("delta").load("s3://your-bucket/patients/")
    
    # Add partitioning columns
    medical_claims_df = medical_claims_df.withColumn("service_year", year("service_date"))
    medical_claims_df = medical_claims_df.withColumn("service_month", month("service_date"))
    
    pharmacy_claims_df = pharmacy_claims_df.withColumn("fill_year", year("fill_date"))
    pharmacy_claims_df = pharmacy_claims_df.withColumn("fill_month", month("fill_date"))
    
    patients_df = patients_df.withColumn("enrollment_year", year("enrollment_start_date"))
    
    # Write to Delta tables
    medical_claims_df.write.mode("overwrite").partitionBy("service_year", "service_month").saveAsTable("genetic_testing.medical_claims")
    pharmacy_claims_df.write.mode("overwrite").partitionBy("fill_year", "fill_month").saveAsTable("genetic_testing.pharmacy_claims")
    patients_df.write.mode("overwrite").partitionBy("enrollment_year").saveAsTable("genetic_testing.patients")

load_claims_data()
```

## Pipeline Deployment

### 1. Upload Code to Workspace

```bash
# Upload the entire project to Databricks workspace
# Use Databricks CLI or workspace UI

databricks workspace import_dir ./genetic_testing /Workspace/genetic_testing --overwrite
```

### 2. Create MLflow Experiment

```python
import mlflow

# Create experiment for genetic testing models
experiment_name = "/genetic_testing_experiments"
try:
    experiment_id = mlflow.create_experiment(experiment_name)
except:
    experiment = mlflow.get_experiment_by_name(experiment_name)
    experiment_id = experiment.experiment_id

print(f"Experiment ID: {experiment_id}")
```

### 3. Deploy as Databricks Job

#### Create Job Configuration
```json
{
  "name": "genetic_testing_pipeline",
  "new_cluster": {
    "spark_version": "13.3.x-scala2.12",
    "node_type_id": "i3.xlarge",
    "num_workers": 4,
    "spark_conf": {
      "spark.sql.adaptive.enabled": "true",
      "spark.sql.adaptive.coalescePartitions.enabled": "true"
    }
  },
  "libraries": [
    {
      "pypi": {
        "package": "scikit-learn>=1.3.0"
      }
    },
    {
      "pypi": {
        "package": "xgboost>=1.7.0"
      }
    }
  ],
  "spark_python_task": {
    "python_file": "/Workspace/genetic_testing/src/main.py"
  },
  "timeout_seconds": 7200,
  "max_retries": 2,
  "email_notifications": {
    "on_failure": ["<EMAIL>"],
    "on_success": ["<EMAIL>"]
  }
}
```

#### Deploy Job via CLI
```bash
databricks jobs create --json-file job_config.json
```

### 4. Schedule Pipeline Execution

```python
# Schedule daily execution
from databricks_cli.jobs.api import JobsApi
from databricks_cli.sdk.api_client import ApiClient

api_client = ApiClient(host=databricks_host, token=databricks_token)
jobs_api = JobsApi(api_client)

# Create scheduled run
schedule_config = {
    "quartz_cron_expression": "0 0 2 * * ?",  # Daily at 2 AM
    "timezone_id": "UTC"
}

jobs_api.reset_job(job_id, new_settings={"schedule": schedule_config})
```

## Monitoring and Alerting

### 1. MLflow Tracking

```python
# Enable automatic MLflow tracking
mlflow.autolog()

# Custom metrics tracking
with mlflow.start_run():
    mlflow.log_param("lookback_days", 365)
    mlflow.log_param("similarity_threshold", 0.7)
    mlflow.log_metric("genetic_patients_identified", genetic_count)
    mlflow.log_metric("recommendations_generated", recommendation_count)
```

### 2. Data Quality Monitoring

```python
# Create data quality checks
def monitor_data_quality():
    # Check for data freshness
    latest_claim_date = spark.sql("SELECT MAX(service_date) FROM genetic_testing.medical_claims").collect()[0][0]
    days_since_latest = (datetime.now().date() - latest_claim_date).days
    
    if days_since_latest > 7:
        send_alert(f"Data freshness issue: Latest claim is {days_since_latest} days old")
    
    # Check for data volume
    daily_claims = spark.sql("SELECT COUNT(*) FROM genetic_testing.medical_claims WHERE service_date = CURRENT_DATE - 1").collect()[0][0]
    
    if daily_claims < 1000:  # Adjust threshold as needed
        send_alert(f"Low data volume: Only {daily_claims} claims yesterday")

# Schedule data quality monitoring
monitor_data_quality()
```

### 3. Performance Monitoring

```python
# Monitor pipeline performance
def monitor_performance():
    # Track execution time
    start_time = time.time()
    
    # Run pipeline
    results = pipeline.run_full_pipeline()
    
    execution_time = time.time() - start_time
    
    # Log performance metrics
    mlflow.log_metric("execution_time_seconds", execution_time)
    mlflow.log_metric("patients_processed", results.get("data_summary", {}).get("total_patients", 0))
    
    # Alert if performance degrades
    if execution_time > 3600:  # 1 hour threshold
        send_alert(f"Pipeline execution took {execution_time/60:.1f} minutes")

monitor_performance()
```

## Security Configuration

### 1. Access Control

```sql
-- Grant appropriate permissions
GRANT SELECT ON genetic_testing.* TO `data_scientists`;
GRANT ALL PRIVILEGES ON genetic_testing.* TO `ml_engineers`;

-- Create service principal for automated jobs
CREATE SERVICE PRINCIPAL 'genetic-testing-pipeline';
GRANT SELECT ON genetic_testing.* TO `genetic-testing-pipeline`;
```

### 2. Data Encryption

```python
# Enable encryption at rest and in transit
spark.conf.set("spark.sql.execution.arrow.pyspark.enabled", "true")
spark.conf.set("spark.databricks.delta.preview.enabled", "true")

# Use encrypted storage locations
spark.conf.set("fs.s3a.server-side-encryption-algorithm", "AES256")
```

## Troubleshooting

### Common Issues

1. **Memory Issues**
   - Increase cluster size or enable auto-scaling
   - Optimize Spark configurations
   - Use data sampling for development

2. **Performance Issues**
   - Enable adaptive query execution
   - Optimize table partitioning
   - Use Z-ordering for frequently queried columns

3. **Data Quality Issues**
   - Implement comprehensive data validation
   - Monitor data freshness and completeness
   - Set up automated alerts

### Debugging

```python
# Enable detailed logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Use Spark UI for performance analysis
spark.sparkContext.setLogLevel("INFO")

# Profile memory usage
from pyspark.profiler import BasicProfiler
spark.sparkContext.profiler_collector = BasicProfiler()
```

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review model performance metrics
   - Check data quality reports
   - Monitor resource utilization

2. **Monthly**
   - Retrain models with new data
   - Update ICD-10 code mappings
   - Review and optimize queries

3. **Quarterly**
   - Conduct security audits
   - Review and update configurations
   - Performance optimization review

### Model Updates

```python
# Automated model retraining
def retrain_models():
    # Load latest data
    latest_data = load_recent_data(days=90)
    
    # Retrain risk scoring model
    new_model = train_risk_model(latest_data)
    
    # Evaluate model performance
    performance = evaluate_model(new_model, test_data)
    
    # Deploy if performance is acceptable
    if performance['auc'] > 0.8:
        register_model(new_model, "genetic_risk_scorer")
        
# Schedule monthly retraining
schedule_job(retrain_models, cron="0 0 1 * *")
```

This deployment guide provides a comprehensive framework for deploying and maintaining the genetic testing ML pipeline in production.
