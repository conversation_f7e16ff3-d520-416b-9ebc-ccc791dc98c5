from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import pandas as pd
from datetime import datetime, timedelta

# Neptune Multi-Table Configuration
CATALOG = "healthverity_claims_sample_patient_dataset"
DATABASE = "hv_claims_sample"

TABLES = {
    "diagnosis": f"{CATALOG}.{DATABASE}.diagnosis",
    "medical_claim": f"{CATALOG}.{DATABASE}.medical_claim",
    "pharmacy_claim": f"{CATALOG}.{DATABASE}.pharmacy_claim"
}

# Pipeline Parameters
LOOKBACK_DAYS = 365
MIN_GENETIC_CLAIMS = 1
MIN_CANDIDATE_CLAIMS = 5
SIMILARITY_THRESHOLD = 0.7
COMPREHENSIVE_THRESHOLD = 0.6  # Lower threshold for multi-dimensional analysis

print("🧬 Neptune Enhanced Genetic Testing Pipeline")
print("=" * 55)
print(f"📊 Multi-Table Configuration:")
for name, path in TABLES.items():
    print(f"   {name}: {path}")
print(f"\n⚙️  Enhanced Parameters:")
print(f"   Lookback period: {LOOKBACK_DAYS} days")
print(f"   Comprehensive similarity threshold: {COMPREHENSIVE_THRESHOLD}")

print("📊 Loading all Neptune tables...")

# Load tables with error handling
table_data = {}
table_stats = {}

for table_name, table_path in TABLES.items():
    try:
        df = spark.table(table_path)
        row_count = df.count()
        table_data[table_name] = df
        table_stats[table_name] = {
            'rows': row_count,
            'columns': len(df.columns),
            'available': True
        }
        print(f"✅ {table_name}: {row_count:,} records, {len(df.columns)} columns")
    except Exception as e:
        table_stats[table_name] = {'available': False, 'error': str(e)}
        print(f"❌ {table_name}: Error - {str(e)}")

# Determine available tables
available_tables = [name for name, stats in table_stats.items() if stats.get('available', False)]
print(f"\n✅ Available tables: {available_tables}")

if 'diagnosis' not in available_tables:
    raise ValueError("Diagnosis table is required but not available")

print("🧬 Comprehensive genetic patient identification...")

# Primary: Genetic disorders from diagnosis table
diagnosis_df = table_data['diagnosis']

# Enhanced genetic disorder patterns
genetic_patterns = {
    # Family history (Z80-Z84)
    "Z80": "Family history of malignant neoplasm of digestive organs",
    "Z81": "Family history of malignant neoplasm of trachea, bronchus and lung",
    "Z82": "Family history of certain disabilities and chronic diseases", 
    "Z83": "Family history of other specific disorders",
    "Z84": "Family history of other conditions",
    
    # Chromosomal abnormalities (Q90-Q99)
    "Q90": "Down syndrome", "Q91": "Edwards syndrome and Patau syndrome",
    "Q92": "Other trisomies", "Q93": "Monosomies and deletions",
    "Q95": "Balanced rearrangements", "Q96": "Turner syndrome",
    "Q97": "Other sex chromosome abnormalities, female",
    "Q98": "Other sex chromosome abnormalities, male", 
    "Q99": "Other chromosome abnormalities",
    
    # Metabolic disorders (E70-E88)
    "E70": "Disorders of aromatic amino-acid metabolism",
    "E71": "Disorders of branched-chain amino-acid metabolism",
    "E72": "Other disorders of amino-acid metabolism",
    "E74": "Other disorders of carbohydrate metabolism",
    "E75": "Disorders of sphingolipid metabolism",
    "E76": "Disorders of glycosaminoglycan metabolism",
    "E77": "Disorders of glycoprotein metabolism",
    "E78": "Disorders of lipoprotein metabolism",
    "E79": "Disorders of purine and pyrimidine metabolism",
    
    # Additional genetic indicators
    "Z15": "Genetic susceptibility to malignant neoplasm",
    "Z14": "Genetic carrier status",
    "Q87": "Other specified congenital malformation syndromes"
}

# Find genetic patients
genetic_codes = list(genetic_patterns.keys())
genetic_filter = "|".join([f"^{code}" for code in genetic_codes])

genetic_patients = (
    diagnosis_df
    .filter(col("diagnosis_code").rlike(genetic_filter))
    .groupBy("patient_id")
    .agg(
        count("*").alias("genetic_claim_count"),
        collect_set("diagnosis_code").alias("genetic_icd_codes"),
        min("date_service").alias("first_genetic_diagnosis"),
        max("date_service").alias("last_genetic_diagnosis"),
        countDistinct("diagnosis_code").alias("unique_genetic_codes")
    )
    .filter(col("genetic_claim_count") >= MIN_GENETIC_CLAIMS)
)

genetic_patient_count = genetic_patients.count()
print(f"🧬 Found {genetic_patient_count:,} patients with genetic diagnoses")

if genetic_patient_count == 0:
    print("❌ No genetic patients found. Cannot proceed with pipeline.")
    dbutils.notebook.exit("No genetic patterns detected")

# Cache genetic patients
# genetic_patients.cache()

# Get genetic patient IDs for filtering
genetic_patient_ids = [row.patient_id for row in genetic_patients.select("patient_id").collect()]

print("📈 Building comprehensive patient profiles...")

# 1. DIAGNOSIS UTILIZATION PROFILE
print("\n📊 Building diagnosis utilization profiles...")

diagnosis_profiles = (
    diagnosis_df
    .groupBy("patient_id")
    .agg(
        count("*").alias("total_diagnoses"),
        countDistinct("diagnosis_code").alias("unique_diagnoses"),
        countDistinct("date_service").alias("diagnosis_service_days"),
        min("date_service").alias("first_diagnosis"),
        max("date_service").alias("last_diagnosis")
    )
)

print(f"   Diagnosis profiles: {diagnosis_profiles.count():,} patients")

# 2. MEDICAL CLAIMS UTILIZATION PROFILE (if available)
medical_profiles = None
if 'medical_claim' in available_tables:
    print("\n🏥 Building medical claims utilization profiles...")
    
    medical_df = table_data['medical_claim']
    
    medical_profiles = (
        medical_df
        .groupBy("patient_id")
        .agg(
            count("*").alias("total_medical_claims"),
            countDistinct("claim_id").alias("unique_medical_claims"),
            countDistinct("date_service").alias("medical_service_days"),
            countDistinct("location_of_care").alias("unique_care_locations"),
            countDistinct("pay_type").alias("unique_pay_types"),
            min("date_service").alias("first_medical_service"),
            max("date_service").alias("last_medical_service")
        )
    )
    
    print(f"   Medical profiles: {medical_profiles.count():,} patients")

# 3. PHARMACY UTILIZATION PROFILE (if available)
pharmacy_profiles = None
if 'pharmacy_claim' in available_tables:
    print("\n💊 Building pharmacy utilization profiles...")
    
    pharmacy_df = table_data['pharmacy_claim']
    
    pharmacy_profiles = (
        pharmacy_df
        .groupBy("patient_id")
        .agg(
            count("*").alias("total_pharmacy_claims"),
            countDistinct("claim_id").alias("unique_pharmacy_claims"),
            countDistinct("ndc").alias("unique_medications"),
            sum("dispensed_quantity").alias("total_medication_quantity"),
            sum("days_supply").alias("total_days_supply"),
            avg("days_supply").alias("avg_days_supply"),
            min("date_service").alias("first_pharmacy_service"),
            max("date_service").alias("last_pharmacy_service")
        )
    )
    
    print(f"   Pharmacy profiles: {pharmacy_profiles.count():,} patients")

print("🔗 Creating comprehensive patient profiles...")

# Start with diagnosis profiles as base
comprehensive_profiles = diagnosis_profiles

# Join medical profiles if available
if medical_profiles is not None:
    comprehensive_profiles = comprehensive_profiles.join(
        medical_profiles, "patient_id", "left"
    )
    print("   ✅ Medical claims data integrated")

# Join pharmacy profiles if available  
if pharmacy_profiles is not None:
    comprehensive_profiles = comprehensive_profiles.join(
        pharmacy_profiles, "patient_id", "left"
    )
    print("   ✅ Pharmacy claims data integrated")

# Fill nulls with zeros for missing data
numeric_columns = [col for col in comprehensive_profiles.columns if col != "patient_id" and "date" not in col.lower()]

for col_name in numeric_columns:
    if col_name in comprehensive_profiles.columns:
        comprehensive_profiles = comprehensive_profiles.fillna({col_name: 0})


total_patients = comprehensive_profiles.count()
print(f"\n📊 Comprehensive profiles created for {total_patients:,} patients")

# Show sample profile
print(f"\n📋 Sample comprehensive patient profile:")
comprehensive_profiles.limit(3).show(truncate=False)

from pyspark.sql.functions import avg, col
from pyspark.sql.types import NumericType

print("🧬 Analyzing genetic patient utilization patterns...")

# Get comprehensive profiles for genetic patients
genetic_comprehensive_profiles = (
    comprehensive_profiles
    .filter(col("patient_id").isin(genetic_patient_ids))
)

genetic_profile_count = genetic_comprehensive_profiles.count()
print(f"📊 Genetic patients with comprehensive profiles: {genetic_profile_count:,}")

# Only include numeric columns for averaging
numeric_cols = [
    f.name for f in comprehensive_profiles.schema.fields
    if isinstance(f.dataType, NumericType) and f.name != "patient_id"
]

genetic_averages = {}
for col_name in numeric_cols:
    avg_val = genetic_comprehensive_profiles.select(avg(col(col_name))).collect()[0][0]
    genetic_averages[col_name] = avg_val if avg_val is not None else 0

print(f"\n📊 Genetic Patient Average Utilization:")
for metric, value in genetic_averages.items():
    print(f"   {metric}: {value:.2f}")

# Compare to overall population
overall_averages = {}
for col_name in numeric_cols:
    avg_val = comprehensive_profiles.select(avg(col(col_name))).collect()[0][0]
    overall_averages[col_name] = avg_val if avg_val is not None else 0

print(f"\n📊 Genetic vs Overall Ratios:")
for metric in genetic_averages.keys():
    if overall_averages[metric] > 0:
        ratio = genetic_averages[metric] / overall_averages[metric]
        print(f"   {metric}: {ratio:.2f}x")

from functools import reduce

print("🎯 Enhanced genetic testing candidate identification...")

# Create multi-dimensional similarity scoring
candidate_profiles = (
    comprehensive_profiles
    .filter(~col("patient_id").isin(genetic_patient_ids))
    .filter(col("total_diagnoses") >= MIN_CANDIDATE_CLAIMS)
)

# Calculate similarity scores across multiple dimensions
similarity_expressions = []

# Diagnosis similarity
if "total_diagnoses" in genetic_averages and genetic_averages["total_diagnoses"] > 0:
    similarity_expressions.append(
        ("diagnosis_similarity", col("total_diagnoses") / genetic_averages["total_diagnoses"])
    )

if "unique_diagnoses" in genetic_averages and genetic_averages["unique_diagnoses"] > 0:
    similarity_expressions.append(
        ("diagnosis_diversity_similarity", col("unique_diagnoses") / genetic_averages["unique_diagnoses"])
    )

# Medical claims similarity (if available)
if medical_profiles is not None:
    if "total_medical_claims" in genetic_averages and genetic_averages["total_medical_claims"] > 0:
        similarity_expressions.append(
            ("medical_claims_similarity", col("total_medical_claims") / genetic_averages["total_medical_claims"])
        )
    if "unique_care_locations" in genetic_averages and genetic_averages["unique_care_locations"] > 0:
        similarity_expressions.append(
            ("care_location_similarity", col("unique_care_locations") / genetic_averages["unique_care_locations"])
        )

# Pharmacy similarity (if available)
if pharmacy_profiles is not None:
    if "unique_medications" in genetic_averages and genetic_averages["unique_medications"] > 0:
        similarity_expressions.append(
            ("medication_diversity_similarity", col("unique_medications") / genetic_averages["unique_medications"])
        )
    if "total_pharmacy_claims" in genetic_averages and genetic_averages["total_pharmacy_claims"] > 0:
        similarity_expressions.append(
            ("pharmacy_claims_similarity", col("total_pharmacy_claims") / genetic_averages["total_pharmacy_claims"])
        )

# Add similarity scores to candidate profiles
for alias, expr in similarity_expressions:
    candidate_profiles = candidate_profiles.withColumn(alias, expr)

# Calculate overall comprehensive similarity score
similarity_columns = [alias for alias, _ in similarity_expressions]
if similarity_columns:
    overall_similarity_expr = (
        reduce(lambda a, b: a + b, [col(sim_col) for sim_col in similarity_columns]) / len(similarity_columns)
    )

    candidate_profiles = candidate_profiles.withColumn("comprehensive_similarity", overall_similarity_expr)

    # Filter by comprehensive threshold
    enhanced_candidates = (
        candidate_profiles
        .filter(col("comprehensive_similarity") >= COMPREHENSIVE_THRESHOLD)
        .orderBy(desc("comprehensive_similarity"))
    )

    enhanced_candidate_count = enhanced_candidates.count()
    print(f"🎯 Found {enhanced_candidate_count:,} enhanced genetic testing candidates!")

    if enhanced_candidate_count > 0:
        print(f"\n📊 Enhanced candidate similarity distribution:")
        display(enhanced_candidates.select("comprehensive_similarity").describe())

if 'enhanced_candidates' in locals() and enhanced_candidate_count > 0:
    print("📋 Generating enhanced genetic testing recommendations...")
    
    # Create comprehensive recommendations with multi-dimensional insights
    enhanced_recommendations = (
        enhanced_candidates
        .withColumn("risk_score", round(col("comprehensive_similarity") * 100, 1))
        .withColumn("priority", 
                   when(col("comprehensive_similarity") >= 1.5, "Critical")
                   .when(col("comprehensive_similarity") >= 1.2, "High")
                   .when(col("comprehensive_similarity") >= 1.0, "Medium")
                   .when(col("comprehensive_similarity") >= 0.8, "Low")
                   .otherwise("Very Low"))
        .withColumn("recommendation_type", lit("Multi-Dimensional Analysis"))
        .withColumn("evidence_strength", 
                   when(col("comprehensive_similarity") >= 1.2, "Strong")
                   .when(col("comprehensive_similarity") >= 1.0, "Moderate")
                   .otherwise("Weak"))
    )
    
    # Add detailed reasoning based on available data
    reasoning_parts = [
        concat(lit("Diagnosis utilization: "), col("total_diagnoses").cast("string"), lit(" claims"))
    ]
    
    if medical_profiles is not None:
        reasoning_parts.append(
            concat(lit(", Medical claims: "), col("total_medical_claims").cast("string"))
        )
    
    if pharmacy_profiles is not None:
        reasoning_parts.append(
            concat(lit(", Medications: "), col("unique_medications").cast("string"))
        )
    
    reasoning_parts.append(
        concat(lit(", Similarity score: "), round(col("comprehensive_similarity"), 2).cast("string"))
    )
    
    # Combine reasoning parts
    full_reasoning = reasoning_parts[0]
    for part in reasoning_parts[1:]:
        full_reasoning = concat(full_reasoning, part)
    
    enhanced_recommendations = enhanced_recommendations.withColumn("recommendation_reason", full_reasoning)
    enhanced_recommendations = enhanced_recommendations.withColumn("recommendation_date", current_date())
    
    # Select final columns
    final_columns = [
        "patient_id", "risk_score", "priority", "evidence_strength",
        "total_diagnoses", "unique_diagnoses"
    ]
    
    # Add medical columns if available
    if medical_profiles is not None:
        final_columns.extend(["total_medical_claims", "unique_care_locations"])
    
    # Add pharmacy columns if available
    if pharmacy_profiles is not None:
        final_columns.extend(["unique_medications", "total_pharmacy_claims"])
    
    final_columns.extend([
        "comprehensive_similarity", "recommendation_reason", "recommendation_date"
    ])
    
    enhanced_recommendations = enhanced_recommendations.select(*final_columns).orderBy(desc("risk_score"))
    
    # Do NOT cache recommendations on serverless
    
    # Show recommendations by priority
    priorities = ["Critical", "High", "Medium", "Low"]
    
    print(f"\n🎯 Enhanced Recommendations by Priority:")
    for priority in priorities:
        priority_count = enhanced_recommendations.filter(col("priority") == priority).count()
        if priority_count > 0:
            print(f"\n📊 {priority} Priority: {priority_count:,} patients")
            display(
                enhanced_recommendations.filter(col("priority") == priority).limit(3)
            )

print("📊 NEPTUNE ENHANCED GENETIC TESTING PIPELINE - EXECUTIVE SUMMARY")
print("=" * 75)

# Data sources summary
print(f"📈 Data Sources Utilized:")
for table_name, stats in table_stats.items():
    if stats.get('available', False):
        print(f"   ✅ {table_name}: {stats['rows']:,} records")
    else:
        print(f"   ❌ {table_name}: Not available")

# Patient analysis summary
print(f"\n👥 Patient Analysis:")
print(f"   Total patients analyzed: {total_patients:,}")
print(f"   Patients with genetic diagnoses: {genetic_patient_count:,}")
genetic_prevalence = (genetic_patient_count / total_patients) * 100
print(f"   Genetic diagnosis prevalence: {genetic_prevalence:.3f}%")

# Multi-dimensional insights
print(f"\n📊 Multi-Dimensional Utilization Insights:")
key_metrics = ["total_diagnoses", "unique_diagnoses"]
if medical_profiles is not None:
    key_metrics.extend(["total_medical_claims", "unique_care_locations"])
if pharmacy_profiles is not None:
    key_metrics.extend(["unique_medications", "total_pharmacy_claims"])

for metric in key_metrics:
    if metric in genetic_averages and metric in overall_averages:
        if overall_averages[metric] > 0:
            ratio = genetic_averages[metric] / overall_averages[metric]
            print(f"   {metric}: Genetic patients {ratio:.1f}x higher than average")

# Enhanced recommendations summary
if 'enhanced_recommendations' in locals():
    print(f"\n🎯 Enhanced Genetic Testing Recommendations:")
    print(f"   Total candidates identified: {enhanced_candidate_count:,}")
    
    for priority in ["Critical", "High", "Medium", "Low"]:
        priority_count = enhanced_recommendations.filter(col("priority") == priority).count()
        if priority_count > 0:
            percentage = (priority_count / enhanced_candidate_count) * 100
            print(f"   {priority} priority: {priority_count:,} ({percentage:.1f}%)")
    
    # Enhanced ROI estimation
    high_priority_count = enhanced_recommendations.filter(col("priority").isin(["Critical", "High"])).count()
    estimated_positive_rate = 0.20  # Higher rate due to enhanced analysis
    estimated_positives = int(high_priority_count * estimated_positive_rate)
    
    print(f"\n💰 Enhanced Impact Estimation:")
    print(f"   High-priority candidates: {high_priority_count:,}")
    print(f"   Estimated positive genetic tests: {estimated_positives:,}")
    print(f"   Enhanced detection rate: {estimated_positive_rate:.0%} (vs 15% standard)")

# Clinical recommendations
print(f"\n💡 Enhanced Clinical Recommendations:")
if 'enhanced_recommendations' in locals() and enhanced_candidate_count > 0:
    print("   1. ✅ Prioritize multi-dimensional high-risk patients")
    print("   2. ✅ Leverage comprehensive utilization patterns for genetic counseling")
    print("   3. ✅ Implement tiered genetic testing protocols")
    print("   4. ✅ Track outcomes across all data dimensions")
    print("   5. ✅ Integrate findings with clinical decision support")
    print("   6. ✅ Consider medication patterns in genetic risk assessment")
else:
    print("   1. ⚠️  Review multi-table data availability and quality")
    print("   2. ⚠️  Validate comprehensive similarity thresholds")
    print("   3. ⚠️  Consider single-table analysis as fallback")

print(f"\n🎉 Enhanced pipeline execution completed successfully!")
print(f"📅 Report generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

print("💾 Saving enhanced results...")

# Uncomment to save results
# enhanced_recommendations.write.mode("overwrite").option("overwriteSchema", "true").saveAsTable("hv_claims_sample.enhanced_genetic_recommendations")
# comprehensive_profiles.write.mode("overwrite").option("overwriteSchema", "true").saveAsTable("hv_claims_sample.comprehensive_patient_profiles")

print("💾 Enhanced results ready for saving")
print("   Uncomment save commands to persist comprehensive analysis")