# Databricks notebook source
# MAGIC %md
# MAGIC # Validate HealthVerity Column Mappings
# MAGIC 
# MAGIC Quick validation test for your identified column mappings:
# MAGIC - `diagnosis_code` for ICD codes
# MAGIC - `patient_id` for patient identifiers  
# MAGIC - `date_service` for service dates

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *

# Your confirmed configuration
TABLE_NAME = "healthverity_claims_sample_patient_dataset.hv_claims_sample.diagnosis"
ICD_COLUMN = "diagnosis_code"
PATIENT_COLUMN = "patient_id"
DATE_COLUMN = "date_service"

print("🔍 Validating HealthVerity Column Mappings")
print("=" * 45)
print(f"Table: {TABLE_NAME}")
print(f"ICD Column: {ICD_COLUMN}")
print(f"Patient Column: {PATIENT_COLUMN}")
print(f"Date Column: {DATE_COLUMN}")

# COMMAND ----------

# Test table access and column validation
try:
    df = spark.table(TABLE_NAME)
    print(f"✅ Table loaded successfully")
    print(f"   Total records: {df.count():,}")
    
    # Validate columns exist
    available_columns = df.columns
    required_columns = [ICD_COLUMN, PATIENT_COLUMN, DATE_COLUMN]
    
    print(f"\n📋 Column Validation:")
    for col_name in required_columns:
        if col_name in available_columns:
            print(f"   ✅ {col_name} - Found")
        else:
            print(f"   ❌ {col_name} - Missing")
    
except Exception as e:
    print(f"❌ Error: {e}")

# COMMAND ----------

# Test data samples from each column
if 'df' in locals():
    print(f"\n📊 Sample Data Validation:")
    
    # Test ICD column
    print(f"\n--- {ICD_COLUMN} samples ---")
    icd_samples = df.select(ICD_COLUMN).filter(col(ICD_COLUMN).isNotNull()).limit(10).collect()
    for i, row in enumerate(icd_samples[:5], 1):
        print(f"   {i}. {row[0]}")
    
    # Test Patient ID column
    print(f"\n--- {PATIENT_COLUMN} samples ---")
    patient_samples = df.select(PATIENT_COLUMN).filter(col(PATIENT_COLUMN).isNotNull()).limit(5).collect()
    for i, row in enumerate(patient_samples, 1):
        print(f"   {i}. {row[0]}")
    
    # Test Date column
    print(f"\n--- {DATE_COLUMN} samples ---")
    date_samples = df.select(DATE_COLUMN).filter(col(DATE_COLUMN).isNotNull()).limit(5).collect()
    for i, row in enumerate(date_samples, 1):
        print(f"   {i}. {row[0]}")

# COMMAND ----------

# Test genetic pattern detection
if 'df' in locals():
    print(f"\n🧬 Testing Genetic Pattern Detection:")
    
    genetic_patterns = ["Z8[0-4]", "Q9[0-9]", "E7[0-9]", "Z15", "Z14"]
    
    total_genetic = 0
    for pattern in genetic_patterns:
        count = df.filter(col(ICD_COLUMN).rlike(f"^{pattern}")).count()
        total_genetic += count
        print(f"   {pattern}: {count:,} matches")
    
    print(f"\n🧬 Total genetic patterns found: {total_genetic:,}")
    
    if total_genetic > 0:
        print("✅ Genetic patterns detected successfully!")
        
        # Show sample genetic codes
        genetic_samples = (
            df.filter(col(ICD_COLUMN).rlike("^(Z8[0-4]|Q9[0-9]|E7[0-9]|Z15|Z14)"))
            .select(ICD_COLUMN)
            .distinct()
            .limit(10)
        )
        
        print(f"\n📋 Sample genetic codes found:")
        genetic_samples.show(truncate=False)
        
    else:
        print("⚠️  No genetic patterns found")

# COMMAND ----------

# Test patient aggregation
if 'df' in locals():
    print(f"\n👥 Testing Patient Aggregation:")
    
    # Count unique patients
    unique_patients = df.select(PATIENT_COLUMN).distinct().count()
    total_records = df.count()
    
    print(f"   Total records: {total_records:,}")
    print(f"   Unique patients: {unique_patients:,}")
    print(f"   Records per patient: {total_records/unique_patients:.1f}")
    
    # Test patient-level aggregation
    patient_summary = (
        df.groupBy(PATIENT_COLUMN)
        .agg(
            count("*").alias("claim_count"),
            countDistinct(ICD_COLUMN).alias("unique_diagnoses")
        )
        .limit(5)
    )
    
    print(f"\n📊 Sample patient aggregations:")
    patient_summary.show()

# COMMAND ----------

# Test date functionality
if 'df' in locals():
    print(f"\n📅 Testing Date Functionality:")
    
    try:
        # Get date range
        date_stats = df.select(
            min(col(DATE_COLUMN)).alias("min_date"),
            max(col(DATE_COLUMN)).alias("max_date"),
            count(col(DATE_COLUMN)).alias("non_null_dates")
        ).collect()[0]
        
        print(f"   Date range: {date_stats.min_date} to {date_stats.max_date}")
        print(f"   Non-null dates: {date_stats.non_null_dates:,}")
        
        # Test date filtering
        recent_count = df.filter(col(DATE_COLUMN) >= "2020-01-01").count()
        print(f"   Records since 2020: {recent_count:,}")
        
        print("✅ Date operations working correctly!")
        
    except Exception as e:
        print(f"❌ Date operation error: {e}")

# COMMAND ----------

# Final validation summary
print(f"\n📊 VALIDATION SUMMARY")
print("=" * 25)

validation_results = {
    "table_access": 'df' in locals(),
    "columns_exist": all(col in df.columns for col in [ICD_COLUMN, PATIENT_COLUMN, DATE_COLUMN]) if 'df' in locals() else False,
    "genetic_patterns": 'total_genetic' in locals() and total_genetic > 0,
    "patient_aggregation": 'unique_patients' in locals() and unique_patients > 0,
    "date_operations": 'date_stats' in locals()
}

for test, result in validation_results.items():
    status = "✅ PASS" if result else "❌ FAIL"
    print(f"   {test}: {status}")

all_passed = all(validation_results.values())

if all_passed:
    print(f"\n🎉 ALL VALIDATIONS PASSED!")
    print(f"✅ Your column mappings are correct and ready for the full pipeline!")
    print(f"\nNext step: Run the configured genetic testing pipeline")
else:
    print(f"\n⚠️  Some validations failed")
    print(f"Please review the errors above and adjust column mappings if needed")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Validation Complete
# MAGIC 
# MAGIC This notebook validated your HealthVerity column mappings:
# MAGIC 
# MAGIC **Configuration Tested:**
# MAGIC - Table: `healthverity_claims_sample_patient_dataset.hv_claims_sample.diagnosis`
# MAGIC - ICD Column: `diagnosis_code`
# MAGIC - Patient Column: `patient_id`  
# MAGIC - Date Column: `date_service`
# MAGIC 
# MAGIC **If all validations passed**, you're ready to run the full genetic testing pipeline!
# MAGIC 
# MAGIC **Next:** Upload and run `healthverity_genetic_pipeline_configured.py`
