"""
Risk scoring and recommendation engine for genetic testing.
Provides probabilistic risk assessment and actionable recommendations.
"""

import logging
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score, GridSearchCV
from sklearn.metrics import roc_auc_score, precision_recall_curve, classification_report
import pandas as pd
import xgboost as xgb
import lightgbm as lgb

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import (
    col, when, lit, array, struct, collect_list,
    desc, asc, row_number, rank, percent_rank,
    round as spark_round, concat, concat_ws
)
from pyspark.sql.window import Window

from ..data.schemas import GeneticTestingRecommendation, GeneticRiskLevel
from ..utils.config import get_config

logger = logging.getLogger(__name__)


class GeneticRiskScorer:
    """Scores patients for genetic disorder risk using ML models."""
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = get_config()
        self.model = None
        self.feature_importance = None
        
    def train_risk_model(self,
                        genetic_patients_df: DataFrame,
                        control_patients_df: DataFrame,
                        features_df: DataFrame) -> Dict[str, Any]:
        """
        Train risk scoring model using genetic and control patients.
        
        Args:
            genetic_patients_df: DataFrame with genetic patients (positive cases)
            control_patients_df: DataFrame with control patients (negative cases)
            features_df: DataFrame with patient features
            
        Returns:
            Dictionary with training results and model performance
        """
        logger.info("Training genetic risk scoring model")
        
        # Prepare training data
        training_data = self._prepare_training_data(
            genetic_patients_df, control_patients_df, features_df
        )
        
        # Train model
        model_results = self._train_model(training_data)
        
        # Evaluate model
        evaluation_results = self._evaluate_model(training_data, model_results['model'])
        
        # Store model and feature importance
        self.model = model_results['model']
        self.feature_importance = model_results['feature_importance']
        
        results = {
            **model_results,
            **evaluation_results,
            "training_data_size": len(training_data),
            "positive_cases": len(training_data[training_data['target'] == 1]),
            "negative_cases": len(training_data[training_data['target'] == 0])
        }
        
        logger.info(f"Model training completed. AUC: {results.get('auc_score', 'N/A')}")
        
        return results
    
    def _prepare_training_data(self,
                              genetic_patients_df: DataFrame,
                              control_patients_df: DataFrame,
                              features_df: DataFrame) -> pd.DataFrame:
        """Prepare training dataset with labels."""
        
        # Get genetic patient IDs
        genetic_ids = [row.patient_id for row in 
                      genetic_patients_df.select("patient_id").collect()]
        
        # Get control patient IDs
        control_ids = [row.patient_id for row in 
                      control_patients_df.select("patient_id").collect()]
        
        # Filter features for training patients
        training_features = features_df.filter(
            col("patient_id").isin(genetic_ids + control_ids)
        )
        
        # Convert to Pandas
        training_pandas = training_features.toPandas()
        
        # Add target labels
        training_pandas['target'] = training_pandas['patient_id'].apply(
            lambda x: 1 if x in genetic_ids else 0
        )
        
        # Select feature columns
        feature_cols = [col for col in training_pandas.columns 
                       if col not in ['patient_id', 'target'] and 
                       training_pandas[col].dtype in ['int64', 'float64']]
        
        # Fill missing values
        training_pandas[feature_cols] = training_pandas[feature_cols].fillna(0)
        
        return training_pandas[['patient_id', 'target'] + feature_cols]
    
    def _train_model(self, training_data: pd.DataFrame) -> Dict[str, Any]:
        """Train the risk scoring model."""
        
        # Prepare features and target
        feature_cols = [col for col in training_data.columns 
                       if col not in ['patient_id', 'target']]
        X = training_data[feature_cols]
        y = training_data['target']
        
        # Get model type from config
        model_type = self.config.model_config.risk_model_type
        
        if model_type == "xgboost":
            model = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            )
        elif model_type == "lightgbm":
            model = lgb.LGBMClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            )
        elif model_type == "random_forest":
            model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42
            )
        else:  # logistic_regression
            model = LogisticRegression(
                random_state=42,
                max_iter=1000
            )
        
        # Train model
        model.fit(X, y)
        
        # Get feature importance
        if hasattr(model, 'feature_importances_'):
            feature_importance = dict(zip(feature_cols, model.feature_importances_))
        elif hasattr(model, 'coef_'):
            feature_importance = dict(zip(feature_cols, abs(model.coef_[0])))
        else:
            feature_importance = {}
        
        return {
            "model": model,
            "feature_importance": feature_importance,
            "feature_columns": feature_cols
        }
    
    def _evaluate_model(self, training_data: pd.DataFrame, model) -> Dict[str, Any]:
        """Evaluate model performance."""
        
        feature_cols = [col for col in training_data.columns 
                       if col not in ['patient_id', 'target']]
        X = training_data[feature_cols]
        y = training_data['target']
        
        # Cross-validation scores
        cv_scores = cross_val_score(model, X, y, cv=5, scoring='roc_auc')
        
        # Predictions for full dataset
        y_pred_proba = model.predict_proba(X)[:, 1]
        y_pred = model.predict(X)
        
        # Calculate metrics
        auc_score = roc_auc_score(y, y_pred_proba)
        
        # Classification report
        class_report = classification_report(y, y_pred, output_dict=True)
        
        return {
            "auc_score": float(auc_score),
            "cv_auc_mean": float(cv_scores.mean()),
            "cv_auc_std": float(cv_scores.std()),
            "precision": float(class_report['1']['precision']),
            "recall": float(class_report['1']['recall']),
            "f1_score": float(class_report['1']['f1-score'])
        }
    
    def score_patients(self, patients_df: DataFrame) -> DataFrame:
        """
        Score patients for genetic disorder risk.
        
        Args:
            patients_df: DataFrame with patient features
            
        Returns:
            DataFrame with risk scores
        """
        if self.model is None:
            raise ValueError("Model must be trained before scoring patients")
        
        logger.info("Scoring patients for genetic disorder risk")
        
        # Convert to Pandas for scoring
        patients_pandas = patients_df.toPandas()
        
        # Prepare features
        feature_cols = [col for col in patients_pandas.columns 
                       if col in self.feature_importance.keys()]
        
        # Fill missing values
        patients_pandas[feature_cols] = patients_pandas[feature_cols].fillna(0)
        
        # Get risk scores
        risk_scores = self.model.predict_proba(patients_pandas[feature_cols])[:, 1]
        
        # Add scores to DataFrame
        patients_pandas['risk_score'] = risk_scores
        
        # Convert back to Spark DataFrame
        scored_df = self.spark.createDataFrame(patients_pandas[['patient_id', 'risk_score']])
        
        # Add risk levels
        scored_df = scored_df.withColumn("risk_level",
                                        when(col("risk_score") >= 0.8, "very_high")
                                        .when(col("risk_score") >= 0.6, "high")
                                        .when(col("risk_score") >= 0.4, "moderate")
                                        .otherwise("low"))
        
        # Add percentile ranks
        window_spec = Window.orderBy(desc("risk_score"))
        scored_df = scored_df.withColumn("risk_percentile",
                                        percent_rank().over(window_spec) * 100)
        
        logger.info(f"Scored {scored_df.count()} patients")
        
        return scored_df


class GeneticTestingRecommendationEngine:
    """Generates genetic testing recommendations based on risk scores and similarity."""
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = get_config()
    
    def generate_recommendations(self,
                               risk_scores_df: DataFrame,
                               similarity_results_df: DataFrame,
                               patient_features_df: DataFrame) -> DataFrame:
        """
        Generate genetic testing recommendations.
        
        Args:
            risk_scores_df: DataFrame with risk scores
            similarity_results_df: DataFrame with similarity results
            patient_features_df: DataFrame with patient features
            
        Returns:
            DataFrame with recommendations
        """
        logger.info("Generating genetic testing recommendations")
        
        # Join risk scores with similarity results
        combined_df = risk_scores_df.join(
            similarity_results_df.select(
                col("undiagnosed_patient_id").alias("patient_id"),
                "max_similarity_score",
                "best_match_genetic_patient",
                "top_similar_genetic_patients",
                "similarity_category"
            ),
            "patient_id",
            "inner"
        )
        
        # Join with patient features for context
        recommendations_df = combined_df.join(
            patient_features_df.select(
                "patient_id", "medical_claim_count", "pharmacy_claim_count",
                "total_medical_cost", "total_pharmacy_cost",
                "all_primary_diagnoses", "all_therapeutic_classes"
            ),
            "patient_id",
            "left"
        )
        
        # Calculate confidence scores
        recommendations_df = self._calculate_confidence_scores(recommendations_df)
        
        # Generate recommendation reasoning
        recommendations_df = self._generate_reasoning(recommendations_df)
        
        # Recommend specific genetic tests
        recommendations_df = self._recommend_genetic_tests(recommendations_df)
        
        # Filter and rank recommendations
        final_recommendations = self._filter_and_rank_recommendations(recommendations_df)
        
        logger.info(f"Generated {final_recommendations.count()} recommendations")
        
        return final_recommendations
    
    def _calculate_confidence_scores(self, df: DataFrame) -> DataFrame:
        """Calculate confidence scores for recommendations."""
        
        # Confidence based on risk score and similarity
        df = df.withColumn("confidence",
                          (col("risk_score") * 0.6 + col("max_similarity_score") * 0.4))
        
        # Adjust confidence based on claim volume
        df = df.withColumn("confidence",
                          when(col("medical_claim_count") + col("pharmacy_claim_count") < 5, 
                               col("confidence") * 0.8)
                          .otherwise(col("confidence")))
        
        # Cap confidence at 1.0
        df = df.withColumn("confidence",
                          when(col("confidence") > 1.0, 1.0)
                          .otherwise(col("confidence")))
        
        return df
    
    def _generate_reasoning(self, df: DataFrame) -> DataFrame:
        """Generate human-readable reasoning for recommendations."""
        
        df = df.withColumn("reasoning",
                          concat_ws(" ",
                                   concat(lit("High genetic disorder risk (score: "), 
                                         spark_round(col("risk_score"), 3), lit(").")),
                                   concat(lit("Similar to genetic patient "), 
                                         col("best_match_genetic_patient"),
                                         lit(" (similarity: "), 
                                         spark_round(col("max_similarity_score"), 3), lit(").")),
                                   when(col("medical_claim_count") > 10,
                                        concat(lit("High medical utilization ("), 
                                              col("medical_claim_count"), lit(" claims).")))
                                   .otherwise(lit("")),
                                   when(col("total_medical_cost") > 10000,
                                        concat(lit("High medical costs ($"), 
                                              spark_round(col("total_medical_cost"), 0), lit(").")))
                                   .otherwise(lit(""))))
        
        return df
    
    def _recommend_genetic_tests(self, df: DataFrame) -> DataFrame:
        """Recommend specific genetic tests based on patient profile."""
        
        # Default comprehensive panel
        df = df.withColumn("recommended_tests", 
                          array(lit("Comprehensive Genetic Panel")))
        
        # Add specific tests based on conditions
        # This would be expanded based on actual clinical guidelines
        df = df.withColumn("recommended_tests",
                          when(col("risk_level") == "very_high",
                               array(lit("Comprehensive Cancer Panel"),
                                    lit("Metabolic Disorder Panel"),
                                    lit("Pharmacogenomic Testing")))
                          .when(col("risk_level") == "high",
                               array(lit("Targeted Genetic Panel"),
                                    lit("Pharmacogenomic Testing")))
                          .otherwise(array(lit("Basic Genetic Screening"))))
        
        return df
    
    def _filter_and_rank_recommendations(self, df: DataFrame) -> DataFrame:
        """Filter and rank recommendations by priority."""
        
        # Filter by minimum thresholds
        min_risk_threshold = self.config.settings.risk_threshold
        filtered_df = df.filter(
            (col("risk_score") >= min_risk_threshold) &
            (col("confidence") >= 0.5)
        )
        
        # Rank by combined score
        filtered_df = filtered_df.withColumn("priority_score",
                                           col("risk_score") * 0.5 + 
                                           col("confidence") * 0.3 + 
                                           col("max_similarity_score") * 0.2)
        
        # Add ranking
        window_spec = Window.orderBy(desc("priority_score"))
        ranked_df = filtered_df.withColumn("recommendation_rank",
                                         row_number().over(window_spec))
        
        # Add timestamp
        ranked_df = ranked_df.withColumn("created_at", lit(datetime.now()))
        
        return ranked_df
    
    def create_recommendation_summary(self, recommendations_df: DataFrame) -> Dict[str, Any]:
        """Create summary statistics for recommendations."""
        
        # Convert to Pandas for analysis
        recommendations_pandas = recommendations_df.toPandas()
        
        summary = {
            "total_recommendations": len(recommendations_pandas),
            "risk_level_distribution": recommendations_pandas['risk_level'].value_counts().to_dict(),
            "avg_risk_score": float(recommendations_pandas['risk_score'].mean()),
            "avg_confidence": float(recommendations_pandas['confidence'].mean()),
            "avg_similarity_score": float(recommendations_pandas['max_similarity_score'].mean()),
            "top_10_patients": recommendations_pandas.head(10)[
                ['patient_id', 'risk_score', 'confidence', 'risk_level']
            ].to_dict('records')
        }
        
        return summary
    
    def export_recommendations_for_review(self, 
                                        recommendations_df: DataFrame,
                                        output_path: str) -> None:
        """Export recommendations to file for clinical review."""
        
        # Select key columns for review
        export_df = recommendations_df.select(
            "patient_id",
            "risk_score",
            "risk_level", 
            "confidence",
            "max_similarity_score",
            "recommended_tests",
            "reasoning",
            "recommendation_rank"
        ).orderBy("recommendation_rank")
        
        # Write to file
        export_df.coalesce(1).write.mode("overwrite").option("header", "true").csv(output_path)
        
        logger.info(f"Exported recommendations to {output_path}")
