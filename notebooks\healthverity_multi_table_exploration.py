# Databricks notebook source
# MAGIC %md
# MAGIC # HealthVerity Multi-Table Data Exploration
# MAGIC 
# MAGIC This notebook explores all available HealthVerity tables to understand the complete data structure for enhanced genetic testing analysis.
# MAGIC 
# MAGIC **Available Tables:**
# MAGIC - `diagnosis` - Diagnosis codes and dates
# MAGIC - `medical_claim` - Medical claims with service details
# MAGIC - `pharmacy_claim` - Pharmacy claims with medication details

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *

# HealthVerity Table Configuration
CATALOG = "healthverity_claims_sample_patient_dataset"
DATABASE = "hv_claims_sample"

TABLES = {
    "diagnosis": f"{CATALOG}.{DATABASE}.diagnosis",
    "medical_claim": f"{CATALOG}.{DATABASE}.medical_claim", 
    "pharmacy_claim": f"{CATALOG}.{DATABASE}.pharmacy_claim"
}

print("🔍 HealthVerity Multi-Table Data Exploration")
print("=" * 50)
print(f"📊 Tables to explore:")
for name, full_name in TABLES.items():
    print(f"   {name}: {full_name}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 1: Explore All Table Schemas

# COMMAND ----------

def explore_table_schema(table_name, full_table_path):
    """Explore and display table schema and basic info."""
    print(f"\n{'='*60}")
    print(f"📊 EXPLORING TABLE: {table_name.upper()}")
    print(f"{'='*60}")
    
    try:
        # Load table
        df = spark.table(full_table_path)
        
        # Basic info
        row_count = df.count()
        col_count = len(df.columns)
        
        print(f"✅ Table loaded successfully!")
        print(f"   Total records: {row_count:,}")
        print(f"   Total columns: {col_count}")
        
        # Schema
        print(f"\n📋 Schema:")
        df.printSchema()
        
        # Column list
        print(f"\n📝 Column Names:")
        for i, col_name in enumerate(df.columns, 1):
            print(f"   {i:2d}. {col_name}")
        
        # Sample data
        print(f"\n📄 Sample Data (first 3 rows):")
        df.limit(3).show(truncate=False)
        
        # Date columns analysis
        date_columns = [col for col in df.columns if 'date' in col.lower()]
        if date_columns:
            print(f"\n📅 Date Columns Analysis:")
            for date_col in date_columns:
                try:
                    date_stats = df.select(
                        min(col(date_col)).alias("min_date"),
                        max(col(date_col)).alias("max_date"),
                        count(col(date_col)).alias("non_null_count")
                    ).collect()[0]
                    
                    print(f"   {date_col}:")
                    print(f"     Range: {date_stats.min_date} to {date_stats.max_date}")
                    print(f"     Non-null: {date_stats.non_null_count:,}")
                except Exception as e:
                    print(f"   {date_col}: Error analyzing - {str(e)}")
        
        return df, row_count, col_count
        
    except Exception as e:
        print(f"❌ Error loading table: {str(e)}")
        return None, 0, 0

# Explore each table
table_info = {}
for table_name, full_path in TABLES.items():
    df, rows, cols = explore_table_schema(table_name, full_path)
    table_info[table_name] = {
        'df': df,
        'rows': rows,
        'cols': cols,
        'path': full_path
    }

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 2: Patient ID Consistency Check

# COMMAND ----------

print("👥 Checking Patient ID Consistency Across Tables")
print("=" * 50)

patient_counts = {}

for table_name, info in table_info.items():
    if info['df'] is not None:
        try:
            unique_patients = info['df'].select("patient_id").distinct().count()
            patient_counts[table_name] = unique_patients
            print(f"   {table_name}: {unique_patients:,} unique patients")
        except Exception as e:
            print(f"   {table_name}: Error counting patients - {str(e)}")

# Find patient overlap
if len(patient_counts) > 1:
    print(f"\n🔗 Patient Overlap Analysis:")
    
    # Get patient sets
    patient_sets = {}
    for table_name, info in table_info.items():
        if info['df'] is not None:
            try:
                patients = set([row.patient_id for row in info['df'].select("patient_id").distinct().collect()])
                patient_sets[table_name] = patients
                print(f"   {table_name}: {len(patients):,} unique patient IDs")
            except:
                print(f"   {table_name}: Error getting patient set")
    
    # Calculate overlaps
    if len(patient_sets) >= 2:
        table_names = list(patient_sets.keys())
        for i in range(len(table_names)):
            for j in range(i+1, len(table_names)):
                table1, table2 = table_names[i], table_names[j]
                overlap = len(patient_sets[table1] & patient_sets[table2])
                print(f"   {table1} ∩ {table2}: {overlap:,} patients")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 3: Diagnosis Table Deep Dive

# COMMAND ----------

if table_info['diagnosis']['df'] is not None:
    print("🧬 Diagnosis Table Deep Dive")
    print("=" * 35)
    
    diagnosis_df = table_info['diagnosis']['df']
    
    # ICD code analysis
    print(f"\n📊 ICD Code Analysis:")
    icd_stats = diagnosis_df.select(
        count("diagnosis_code").alias("total_diagnoses"),
        countDistinct("diagnosis_code").alias("unique_codes"),
        countDistinct("patient_id").alias("unique_patients")
    ).collect()[0]
    
    print(f"   Total diagnosis records: {icd_stats.total_diagnoses:,}")
    print(f"   Unique ICD codes: {icd_stats.unique_codes:,}")
    print(f"   Unique patients: {icd_stats.unique_patients:,}")
    print(f"   Avg diagnoses per patient: {icd_stats.total_diagnoses/icd_stats.unique_patients:.1f}")
    
    # Top ICD codes
    print(f"\n🔝 Top 15 Most Common ICD Codes:")
    top_codes = (
        diagnosis_df
        .groupBy("diagnosis_code")
        .count()
        .orderBy(desc("count"))
        .limit(15)
    )
    top_codes.show()
    
    # Genetic pattern search
    print(f"\n🧬 Genetic Disorder Pattern Search:")
    genetic_patterns = ["Z8[0-4]", "Q9[0-9]", "E7[0-9]", "Z15", "Z14"]
    
    total_genetic = 0
    for pattern in genetic_patterns:
        count = diagnosis_df.filter(col("diagnosis_code").rlike(f"^{pattern}")).count()
        total_genetic += count
        print(f"   Pattern {pattern}: {count:,} records")
    
    print(f"   Total genetic patterns: {total_genetic:,}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 4: Medical Claims Table Analysis

# COMMAND ----------

if table_info['medical_claim']['df'] is not None:
    print("🏥 Medical Claims Table Analysis")
    print("=" * 35)
    
    medical_df = table_info['medical_claim']['df']
    
    # Basic statistics
    print(f"\n📊 Medical Claims Statistics:")
    medical_stats = medical_df.select(
        count("claim_id").alias("total_claims"),
        countDistinct("claim_id").alias("unique_claims"),
        countDistinct("patient_id").alias("unique_patients")
    ).collect()[0]
    
    print(f"   Total medical claims: {medical_stats.total_claims:,}")
    print(f"   Unique claim IDs: {medical_stats.unique_claims:,}")
    print(f"   Unique patients: {medical_stats.unique_patients:,}")
    print(f"   Avg claims per patient: {medical_stats.total_claims/medical_stats.unique_patients:.1f}")
    
    # Location of care analysis
    if "location_of_care" in medical_df.columns:
        print(f"\n🏥 Location of Care Distribution:")
        location_dist = (
            medical_df
            .groupBy("location_of_care")
            .count()
            .orderBy(desc("count"))
            .limit(10)
        )
        location_dist.show()
    
    # Pay type analysis
    if "pay_type" in medical_df.columns:
        print(f"\n💳 Pay Type Distribution:")
        pay_type_dist = (
            medical_df
            .groupBy("pay_type")
            .count()
            .orderBy(desc("count"))
        )
        pay_type_dist.show()
    
    # Service duration analysis
    if "date_service" in medical_df.columns and "date_service_end" in medical_df.columns:
        print(f"\n⏱️  Service Duration Analysis:")
        duration_df = (
            medical_df
            .filter(col("date_service_end").isNotNull())
            .withColumn("service_days", 
                       datediff(col("date_service_end"), col("date_service")) + 1)
            .filter(col("service_days") >= 0)
        )
        
        duration_stats = duration_df.select("service_days").describe()
        duration_stats.show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 5: Pharmacy Claims Table Analysis

# COMMAND ----------

if table_info['pharmacy_claim']['df'] is not None:
    print("💊 Pharmacy Claims Table Analysis")
    print("=" * 35)
    
    pharmacy_df = table_info['pharmacy_claim']['df']
    
    # Basic statistics
    print(f"\n📊 Pharmacy Claims Statistics:")
    pharmacy_stats = pharmacy_df.select(
        count("claim_id").alias("total_claims"),
        countDistinct("claim_id").alias("unique_claims"),
        countDistinct("patient_id").alias("unique_patients"),
        countDistinct("ndc_code").alias("unique_medications")
    ).collect()[0]
    
    print(f"   Total pharmacy claims: {pharmacy_stats.total_claims:,}")
    print(f"   Unique claim IDs: {pharmacy_stats.unique_claims:,}")
    print(f"   Unique patients: {pharmacy_stats.unique_patients:,}")
    print(f"   Unique medications (NDC): {pharmacy_stats.unique_medications:,}")
    print(f"   Avg claims per patient: {pharmacy_stats.total_claims/pharmacy_stats.unique_patients:.1f}")
    
    # Top medications by frequency
    print(f"\n💊 Top 15 Most Prescribed Medications (by NDC):")
    top_meds = (
        pharmacy_df
        .groupBy("ndc_code")
        .count()
        .orderBy(desc("count"))
        .limit(15)
    )
    top_meds.show()
    
    # Quantity analysis
    if "quantity" in pharmacy_df.columns:
        print(f"\n📦 Quantity Distribution:")
        quantity_stats = pharmacy_df.select("quantity").describe()
        quantity_stats.show()
    
    # Days supply analysis
    if "days_supply" in pharmacy_df.columns:
        print(f"\n📅 Days Supply Distribution:")
        days_supply_stats = pharmacy_df.select("days_supply").describe()
        days_supply_stats.show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 6: Cross-Table Patient Analysis

# COMMAND ----------

print("🔗 Cross-Table Patient Analysis")
print("=" * 35)

# Find patients with data in all tables
all_tables_available = all(info['df'] is not None for info in table_info.values())

if all_tables_available:
    # Get patients from each table
    diagnosis_patients = set([row.patient_id for row in table_info['diagnosis']['df'].select("patient_id").distinct().collect()])
    medical_patients = set([row.patient_id for row in table_info['medical_claim']['df'].select("patient_id").distinct().collect()])
    pharmacy_patients = set([row.patient_id for row in table_info['pharmacy_claim']['df'].select("patient_id").distinct().collect()])
    
    # Calculate overlaps
    all_three = diagnosis_patients & medical_patients & pharmacy_patients
    diag_medical = diagnosis_patients & medical_patients
    diag_pharmacy = diagnosis_patients & pharmacy_patients
    medical_pharmacy = medical_patients & pharmacy_patients
    
    print(f"\n📊 Patient Overlap Summary:")
    print(f"   Patients in all 3 tables: {len(all_three):,}")
    print(f"   Diagnosis + Medical only: {len(diag_medical - pharmacy_patients):,}")
    print(f"   Diagnosis + Pharmacy only: {len(diag_pharmacy - medical_patients):,}")
    print(f"   Medical + Pharmacy only: {len(medical_pharmacy - diagnosis_patients):,}")
    print(f"   Diagnosis only: {len(diagnosis_patients - medical_patients - pharmacy_patients):,}")
    print(f"   Medical only: {len(medical_patients - diagnosis_patients - pharmacy_patients):,}")
    print(f"   Pharmacy only: {len(pharmacy_patients - diagnosis_patients - medical_patients):,}")
    
    # Sample comprehensive patient profile
    if len(all_three) > 0:
        sample_patient = list(all_three)[0]
        print(f"\n👤 Sample Patient Profile (Patient ID: {sample_patient}):")
        
        # Diagnosis data
        patient_diagnoses = table_info['diagnosis']['df'].filter(col("patient_id") == sample_patient)
        diag_count = patient_diagnoses.count()
        print(f"   Diagnoses: {diag_count}")
        if diag_count > 0:
            patient_diagnoses.limit(3).show()
        
        # Medical claims
        patient_medical = table_info['medical_claim']['df'].filter(col("patient_id") == sample_patient)
        medical_count = patient_medical.count()
        print(f"   Medical claims: {medical_count}")
        if medical_count > 0:
            patient_medical.limit(3).show()
        
        # Pharmacy claims
        patient_pharmacy = table_info['pharmacy_claim']['df'].filter(col("patient_id") == sample_patient)
        pharmacy_count = patient_pharmacy.count()
        print(f"   Pharmacy claims: {pharmacy_count}")
        if pharmacy_count > 0:
            patient_pharmacy.limit(3).show()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 7: Data Quality Summary

# COMMAND ----------

print("📊 DATA QUALITY SUMMARY")
print("=" * 30)

for table_name, info in table_info.items():
    if info['df'] is not None:
        print(f"\n📋 {table_name.upper()} Table:")
        print(f"   ✅ Accessible: Yes")
        print(f"   📊 Records: {info['rows']:,}")
        print(f"   📝 Columns: {info['cols']}")
        
        # Check for nulls in key columns
        df = info['df']
        if "patient_id" in df.columns:
            null_patients = df.filter(col("patient_id").isNull()).count()
            print(f"   👥 Null patient_ids: {null_patients:,}")
        
        if "date_service" in df.columns:
            null_dates = df.filter(col("date_service").isNull()).count()
            print(f"   📅 Null service dates: {null_dates:,}")
    else:
        print(f"\n📋 {table_name.upper()} Table:")
        print(f"   ❌ Accessible: No")

# Overall assessment
total_records = sum(info['rows'] for info in table_info.values() if info['df'] is not None)
accessible_tables = sum(1 for info in table_info.values() if info['df'] is not None)

print(f"\n🎯 OVERALL ASSESSMENT:")
print(f"   📊 Total records across all tables: {total_records:,}")
print(f"   ✅ Accessible tables: {accessible_tables}/{len(TABLES)}")

if accessible_tables == len(TABLES):
    print(f"   🎉 All tables accessible - Ready for comprehensive genetic testing pipeline!")
else:
    print(f"   ⚠️  Some tables inaccessible - Pipeline will use available data")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Step 8: Generate Table Schema Commands

# COMMAND ----------

print("📋 TABLE SCHEMA COMMANDS")
print("=" * 30)
print("\nRun these commands in your Databricks notebook to see full schemas:")

for table_name, full_path in TABLES.items():
    print(f"\n# {table_name.upper()} table schema:")
    print(f"DESCRIBE {full_path}")

print(f"\n💡 Copy and paste these DESCRIBE commands into separate cells")
print(f"   to see the complete column details for each table.")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Recommendations for Enhanced Genetic Testing Pipeline
# MAGIC 
# MAGIC Based on this multi-table exploration, here are recommendations for building a comprehensive genetic testing pipeline:
# MAGIC 
# MAGIC ### 📊 **Data Availability:**
# MAGIC - Use all available tables for richer patient profiling
# MAGIC - Combine diagnosis, medical claims, and pharmacy data for comprehensive analysis
# MAGIC 
# MAGIC ### 🧬 **Enhanced Genetic Detection:**
# MAGIC - Primary: Diagnosis table for genetic ICD codes
# MAGIC - Secondary: Medical claims for genetic-related procedures
# MAGIC - Tertiary: Pharmacy claims for genetic-related medications
# MAGIC 
# MAGIC ### 📈 **Improved Patient Profiling:**
# MAGIC - Medical utilization patterns (location of care, service duration)
# MAGIC - Pharmacy utilization patterns (medication diversity, adherence)
# MAGIC - Combined utilization scoring for better similarity matching
# MAGIC 
# MAGIC ### 🎯 **Next Steps:**
# MAGIC 1. Run the enhanced multi-table genetic testing pipeline
# MAGIC 2. Validate results against single-table approach
# MAGIC 3. Implement comprehensive patient risk scoring
# MAGIC 4. Generate enriched recommendations with multi-dimensional insights
