"""
ICD-10 genetic disorder classification module.
Identifies patients with genetic disorder diagnoses and related conditions.
"""

import logging
import re
from typing import List, Dict, Set, Tuple, Optional
from dataclasses import dataclass
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import (
    col, when, array_contains, regexp_extract, 
    collect_list, collect_set, count, max as spark_max,
    min as spark_min, desc, asc
)

from ..data.schemas import GENETIC_ICD10_CODES
from ..utils.config import get_config

logger = logging.getLogger(__name__)


@dataclass
class GeneticConditionCategory:
    """Represents a category of genetic conditions."""
    name: str
    icd_patterns: List[str]
    description: str
    priority: int  # Higher priority = more specific/important


class ICD10GeneticClassifier:
    """Classifier for identifying genetic disorders from ICD-10 codes."""
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = get_config()
        
        # Define genetic condition categories
        self.genetic_categories = self._initialize_genetic_categories()
        
        # Compile regex patterns for efficient matching
        self.compiled_patterns = self._compile_patterns()
    
    def _initialize_genetic_categories(self) -> List[GeneticConditionCategory]:
        """Initialize genetic condition categories with ICD-10 patterns."""
        return [
            GeneticConditionCategory(
                name="family_history_malignant",
                icd_patterns=["Z80.*"],
                description="Family history of malignant neoplasm",
                priority=8
            ),
            GeneticConditionCategory(
                name="family_history_mental_behavioral",
                icd_patterns=["Z81.*"],
                description="Family history of mental and behavioral disorders",
                priority=6
            ),
            GeneticConditionCategory(
                name="family_history_chronic_diseases",
                icd_patterns=["Z82.*"],
                description="Family history of certain disabilities and chronic diseases",
                priority=7
            ),
            GeneticConditionCategory(
                name="family_history_other_disorders",
                icd_patterns=["Z83.*"],
                description="Family history of other specific disorders",
                priority=6
            ),
            GeneticConditionCategory(
                name="family_history_other_conditions",
                icd_patterns=["Z84.*"],
                description="Family history of other conditions",
                priority=5
            ),
            GeneticConditionCategory(
                name="chromosomal_abnormalities",
                icd_patterns=["Q90.*", "Q91.*", "Q93.*", "Q95.*", "Q96.*", "Q97.*", "Q98.*", "Q99.*"],
                description="Chromosomal abnormalities",
                priority=10
            ),
            GeneticConditionCategory(
                name="metabolic_disorders",
                icd_patterns=["E70.*", "E71.*", "E72.*", "E74.*", "E75.*", "E76.*", "E77.*", "E78.*", "E79.*", "E80.*"],
                description="Inborn errors of metabolism",
                priority=9
            ),
            GeneticConditionCategory(
                name="congenital_malformations",
                icd_patterns=["Q0[0-9].*", "Q1[0-9].*", "Q2[0-9].*", "Q3[0-9].*", "Q4[0-9].*", "Q5[0-9].*", "Q6[0-9].*", "Q7[0-9].*", "Q8[0-9].*"],
                description="Congenital malformations and deformations",
                priority=8
            ),
            GeneticConditionCategory(
                name="hereditary_cancer_syndromes",
                icd_patterns=["Z15.*", "Z80.0", "Z80.1", "Z80.2", "Z80.3", "Z80.41", "Z80.42", "Z80.43", "Z80.49"],
                description="Genetic susceptibility to malignant neoplasm",
                priority=9
            ),
            GeneticConditionCategory(
                name="genetic_carrier_status",
                icd_patterns=["Z14.*"],
                description="Genetic carrier status",
                priority=7
            )
        ]
    
    def _compile_patterns(self) -> Dict[str, re.Pattern]:
        """Compile regex patterns for efficient matching."""
        patterns = {}
        for category in self.genetic_categories:
            for pattern in category.icd_patterns:
                # Convert ICD pattern to regex
                regex_pattern = pattern.replace("*", ".*").replace(".", r"\.")
                patterns[pattern] = re.compile(f"^{regex_pattern}$")
        return patterns
    
    def classify_icd_code(self, icd_code: str) -> List[str]:
        """
        Classify a single ICD-10 code for genetic relevance.
        
        Args:
            icd_code: ICD-10 code to classify
            
        Returns:
            List of genetic categories that match the code
        """
        if not icd_code:
            return []
        
        # Clean the ICD code
        clean_code = icd_code.strip().upper()
        
        matching_categories = []
        
        for category in self.genetic_categories:
            for pattern in category.icd_patterns:
                if pattern in self.compiled_patterns:
                    if self.compiled_patterns[pattern].match(clean_code):
                        matching_categories.append(category.name)
                        break
        
        return matching_categories
    
    def identify_genetic_patients(self, 
                                 medical_claims_df: DataFrame,
                                 require_primary_diagnosis: bool = False,
                                 min_genetic_claims: int = 1) -> DataFrame:
        """
        Identify patients with genetic disorder diagnoses.
        
        Args:
            medical_claims_df: DataFrame with medical claims
            require_primary_diagnosis: If True, only consider primary diagnoses
            min_genetic_claims: Minimum number of genetic claims required
            
        Returns:
            DataFrame with genetic patients and their classifications
        """
        logger.info("Identifying patients with genetic disorder diagnoses")
        
        # Create genetic code patterns for Spark SQL
        genetic_patterns = []
        for category in self.genetic_categories:
            for pattern in category.icd_patterns:
                # Convert to SQL LIKE pattern
                sql_pattern = pattern.replace("*", "%")
                genetic_patterns.append(sql_pattern)
        
        # Filter claims with genetic diagnoses
        genetic_claims = medical_claims_df.filter(
            col("icd10_primary").rlike("|".join([p.replace("%", ".*") for p in genetic_patterns]))
        )
        
        if not require_primary_diagnosis:
            # Also check secondary diagnoses
            secondary_genetic = medical_claims_df.filter(
                array_contains(col("icd10_secondary"), 
                              regexp_extract(col("icd10_secondary"), "|".join([p.replace("%", ".*") for p in genetic_patterns]), 0))
            )
            genetic_claims = genetic_claims.union(secondary_genetic).distinct()
        
        # Classify each genetic claim
        genetic_claims_classified = self._classify_claims_batch(genetic_claims)
        
        # Aggregate by patient
        patient_genetic_summary = (
            genetic_claims_classified
            .groupBy("patient_id")
            .agg(
                count("*").alias("total_genetic_claims"),
                collect_set("icd10_primary").alias("genetic_icd_codes"),
                collect_set("genetic_category").alias("genetic_categories"),
                spark_min("service_date").alias("first_genetic_diagnosis"),
                spark_max("service_date").alias("last_genetic_diagnosis"),
                spark_max("category_priority").alias("max_priority")
            )
            .filter(col("total_genetic_claims") >= min_genetic_claims)
        )
        
        logger.info(f"Identified {patient_genetic_summary.count()} patients with genetic diagnoses")
        
        return patient_genetic_summary
    
    def _classify_claims_batch(self, claims_df: DataFrame) -> DataFrame:
        """Classify genetic claims in batch using Spark operations."""
        
        # Create classification logic using Spark SQL
        classification_conditions = []
        
        for category in sorted(self.genetic_categories, key=lambda x: x.priority, reverse=True):
            patterns = [p.replace("*", ".*") for p in category.icd_patterns]
            pattern_condition = "|".join(patterns)
            
            classification_conditions.append(
                when(col("icd10_primary").rlike(pattern_condition), category.name)
            )
        
        # Apply classification
        classified_df = claims_df.withColumn(
            "genetic_category",
            classification_conditions[0] if classification_conditions else None
        )
        
        # Add category priority for ranking
        priority_mapping = {cat.name: cat.priority for cat in self.genetic_categories}
        
        for category_name, priority in priority_mapping.items():
            classified_df = classified_df.withColumn(
                "category_priority",
                when(col("genetic_category") == category_name, priority)
                .otherwise(col("category_priority"))
            )
        
        return classified_df.filter(col("genetic_category").isNotNull())
    
    def get_genetic_risk_indicators(self, 
                                   patient_claims_df: DataFrame,
                                   patient_id: str) -> Dict[str, any]:
        """
        Get genetic risk indicators for a specific patient.
        
        Args:
            patient_claims_df: DataFrame with all claims for the patient
            patient_id: Patient ID to analyze
            
        Returns:
            Dictionary with risk indicators
        """
        patient_claims = patient_claims_df.filter(col("patient_id") == patient_id)
        
        # Classify all ICD codes for this patient
        all_icd_codes = (
            patient_claims
            .select("icd10_primary")
            .distinct()
            .collect()
        )
        
        genetic_indicators = {
            "patient_id": patient_id,
            "genetic_categories": [],
            "risk_factors": [],
            "family_history_indicators": [],
            "metabolic_indicators": [],
            "chromosomal_indicators": [],
            "overall_genetic_risk_score": 0.0
        }
        
        total_risk_score = 0.0
        
        for row in all_icd_codes:
            icd_code = row.icd10_primary
            if icd_code:
                categories = self.classify_icd_code(icd_code)
                
                for category in categories:
                    if category not in genetic_indicators["genetic_categories"]:
                        genetic_indicators["genetic_categories"].append(category)
                    
                    # Add specific risk factors based on category
                    if "family_history" in category:
                        genetic_indicators["family_history_indicators"].append(icd_code)
                        total_risk_score += 0.3
                    elif "metabolic" in category:
                        genetic_indicators["metabolic_indicators"].append(icd_code)
                        total_risk_score += 0.4
                    elif "chromosomal" in category:
                        genetic_indicators["chromosomal_indicators"].append(icd_code)
                        total_risk_score += 0.5
        
        genetic_indicators["overall_genetic_risk_score"] = min(total_risk_score, 1.0)
        
        return genetic_indicators
    
    def generate_genetic_code_mapping(self) -> DataFrame:
        """Generate a mapping of ICD-10 codes to genetic categories."""
        
        mapping_data = []
        
        for category in self.genetic_categories:
            for pattern in category.icd_patterns:
                mapping_data.append({
                    "icd_pattern": pattern,
                    "genetic_category": category.name,
                    "description": category.description,
                    "priority": category.priority
                })
        
        # Convert to Spark DataFrame
        mapping_df = self.spark.createDataFrame(mapping_data)
        
        return mapping_df
    
    def validate_genetic_classification(self, 
                                      test_cases: List[Tuple[str, List[str]]]) -> Dict[str, float]:
        """
        Validate genetic classification against test cases.
        
        Args:
            test_cases: List of (icd_code, expected_categories) tuples
            
        Returns:
            Dictionary with validation metrics
        """
        correct_predictions = 0
        total_predictions = 0
        category_accuracy = {}
        
        for icd_code, expected_categories in test_cases:
            predicted_categories = self.classify_icd_code(icd_code)
            
            # Check if prediction matches expected
            if set(predicted_categories) == set(expected_categories):
                correct_predictions += 1
            
            total_predictions += 1
            
            # Track category-specific accuracy
            for category in expected_categories:
                if category not in category_accuracy:
                    category_accuracy[category] = {"correct": 0, "total": 0}
                
                category_accuracy[category]["total"] += 1
                if category in predicted_categories:
                    category_accuracy[category]["correct"] += 1
        
        overall_accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
        
        category_accuracies = {
            cat: acc["correct"] / acc["total"] if acc["total"] > 0 else 0.0
            for cat, acc in category_accuracy.items()
        }
        
        return {
            "overall_accuracy": overall_accuracy,
            "category_accuracies": category_accuracies,
            "total_test_cases": total_predictions
        }
