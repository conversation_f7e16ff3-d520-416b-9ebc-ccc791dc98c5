"""
Data processing pipeline for claims history analysis.
Handles feature engineering, aggregation, and temporal analysis.
"""

import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import (
    col, when, count, sum as spark_sum, avg, stddev, min as spark_min, max as spark_max,
    datediff, months_between, year, month, dayofweek, quarter,
    collect_list, collect_set, size, array_contains,
    lag, lead, row_number, rank, dense_rank,
    percentile_approx, countDistinct, first, last,
    regexp_extract, split, explode, coalesce
)
from pyspark.sql.window import Window
from pyspark.sql.types import DoubleType, IntegerType

from ..utils.config import get_config

logger = logging.getLogger(__name__)


class ClaimsProcessor:
    """Main processor for claims history data."""
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = get_config()
    
    def process_medical_claims(self, 
                              medical_claims_df: DataFrame,
                              patient_ids: Optional[List[str]] = None) -> DataFrame:
        """
        Process medical claims data with feature engineering.
        
        Args:
            medical_claims_df: Raw medical claims DataFrame
            patient_ids: Optional list of patient IDs to filter
            
        Returns:
            Processed medical claims with engineered features
        """
        logger.info("Processing medical claims data")
        
        df = medical_claims_df
        
        if patient_ids:
            df = df.filter(col("patient_id").isin(patient_ids))
        
        # Add temporal features
        df = self._add_temporal_features(df, "service_date")
        
        # Add cost features
        df = self._add_cost_features(df)
        
        # Add provider features
        df = self._add_provider_features(df)
        
        # Add diagnosis complexity features
        df = self._add_diagnosis_complexity_features(df)
        
        logger.info(f"Processed {df.count()} medical claims")
        
        return df
    
    def process_pharmacy_claims(self, 
                               pharmacy_claims_df: DataFrame,
                               patient_ids: Optional[List[str]] = None) -> DataFrame:
        """
        Process pharmacy claims data with feature engineering.
        
        Args:
            pharmacy_claims_df: Raw pharmacy claims DataFrame
            patient_ids: Optional list of patient IDs to filter
            
        Returns:
            Processed pharmacy claims with engineered features
        """
        logger.info("Processing pharmacy claims data")
        
        df = pharmacy_claims_df
        
        if patient_ids:
            df = df.filter(col("patient_id").isin(patient_ids))
        
        # Add temporal features
        df = self._add_temporal_features(df, "fill_date")
        
        # Add medication features
        df = self._add_medication_features(df)
        
        # Add adherence features
        df = self._add_adherence_features(df)
        
        logger.info(f"Processed {df.count()} pharmacy claims")
        
        return df
    
    def _add_temporal_features(self, df: DataFrame, date_col: str) -> DataFrame:
        """Add temporal features based on date column."""
        
        df = df.withColumn("year", year(col(date_col))) \
               .withColumn("month", month(col(date_col))) \
               .withColumn("quarter", quarter(col(date_col))) \
               .withColumn("day_of_week", dayofweek(col(date_col))) \
               .withColumn("days_from_today", datediff(col("current_date()"), col(date_col)))
        
        # Add seasonal features
        df = df.withColumn("season", 
                          when(col("month").isin([12, 1, 2]), "winter")
                          .when(col("month").isin([3, 4, 5]), "spring")
                          .when(col("month").isin([6, 7, 8]), "summer")
                          .otherwise("fall"))
        
        return df
    
    def _add_cost_features(self, df: DataFrame) -> DataFrame:
        """Add cost-related features."""
        
        # Calculate cost ratios and differences
        df = df.withColumn("cost_ratio", 
                          when(col("claim_amount") > 0, 
                               col("paid_amount") / col("claim_amount"))
                          .otherwise(0.0))
        
        df = df.withColumn("cost_difference", 
                          col("claim_amount") - col("paid_amount"))
        
        # Categorize cost levels
        df = df.withColumn("cost_category",
                          when(col("paid_amount") < 100, "low")
                          .when(col("paid_amount") < 1000, "medium")
                          .when(col("paid_amount") < 5000, "high")
                          .otherwise("very_high"))
        
        return df
    
    def _add_provider_features(self, df: DataFrame) -> DataFrame:
        """Add provider-related features."""
        
        # Extract provider specialty categories
        df = df.withColumn("is_specialist",
                          when(col("provider_specialty").isin([
                              "Internal Medicine", "Family Medicine", "General Practice"
                          ]), False).otherwise(True))
        
        # Categorize place of service
        df = df.withColumn("service_setting",
                          when(col("place_of_service").isin(["11"]), "office")
                          .when(col("place_of_service").isin(["21", "22", "23"]), "hospital")
                          .when(col("place_of_service").isin(["81", "82"]), "lab")
                          .otherwise("other"))
        
        return df
    
    def _add_diagnosis_complexity_features(self, df: DataFrame) -> DataFrame:
        """Add diagnosis complexity features."""
        
        # Count secondary diagnoses
        df = df.withColumn("secondary_diagnosis_count", 
                          size(col("icd10_secondary")))
        
        # Count CPT codes
        df = df.withColumn("cpt_code_count", 
                          size(col("cpt_codes")))
        
        # Calculate complexity score
        df = df.withColumn("complexity_score",
                          (col("secondary_diagnosis_count") * 0.3 + 
                           col("cpt_code_count") * 0.2))
        
        return df
    
    def _add_medication_features(self, df: DataFrame) -> DataFrame:
        """Add medication-related features."""
        
        # Calculate daily dose (quantity / days_supply)
        df = df.withColumn("daily_dose",
                          when(col("days_supply") > 0,
                               col("quantity") / col("days_supply"))
                          .otherwise(0.0))
        
        # Categorize therapeutic classes
        df = df.withColumn("is_chronic_medication",
                          when(col("therapeutic_class").rlike(
                              "(?i)(diabetes|hypertension|cholesterol|depression|anxiety)"
                          ), True).otherwise(False))
        
        # Calculate cost per day
        df = df.withColumn("cost_per_day",
                          when(col("days_supply") > 0,
                               col("paid_amount") / col("days_supply"))
                          .otherwise(0.0))
        
        return df
    
    def _add_adherence_features(self, df: DataFrame) -> DataFrame:
        """Add medication adherence features."""
        
        # Calculate days between fills for same medication
        window_spec = Window.partitionBy("patient_id", "ndc_code").orderBy("fill_date")
        
        df = df.withColumn("previous_fill_date", lag("fill_date").over(window_spec))
        df = df.withColumn("days_between_fills", 
                          datediff(col("fill_date"), col("previous_fill_date")))
        
        # Calculate adherence ratio (days_supply / days_between_fills)
        df = df.withColumn("adherence_ratio",
                          when((col("days_between_fills") > 0) & (col("days_supply") > 0),
                               col("days_supply") / col("days_between_fills"))
                          .otherwise(1.0))
        
        return df
    
    def aggregate_patient_features(self, 
                                  medical_claims_df: DataFrame,
                                  pharmacy_claims_df: DataFrame,
                                  lookback_days: int = 365) -> DataFrame:
        """
        Aggregate claims data to create patient-level features.
        
        Args:
            medical_claims_df: Processed medical claims
            pharmacy_claims_df: Processed pharmacy claims
            lookback_days: Number of days to look back
            
        Returns:
            DataFrame with patient-level aggregated features
        """
        logger.info("Aggregating patient-level features")
        
        # Filter to lookback period
        cutoff_date = (datetime.now() - timedelta(days=lookback_days)).strftime("%Y-%m-%d")
        
        medical_recent = medical_claims_df.filter(col("service_date") >= cutoff_date)
        pharmacy_recent = pharmacy_claims_df.filter(col("fill_date") >= cutoff_date)
        
        # Aggregate medical claims features
        medical_agg = self._aggregate_medical_features(medical_recent)
        
        # Aggregate pharmacy claims features
        pharmacy_agg = self._aggregate_pharmacy_features(pharmacy_recent)
        
        # Combine features
        patient_features = medical_agg.join(pharmacy_agg, "patient_id", "outer")
        
        # Fill nulls with zeros
        numeric_cols = [field.name for field in patient_features.schema.fields 
                       if field.dataType in [DoubleType(), IntegerType()]]
        
        for col_name in numeric_cols:
            patient_features = patient_features.withColumn(col_name, 
                                                          coalesce(col(col_name), lit(0)))
        
        logger.info(f"Generated features for {patient_features.count()} patients")
        
        return patient_features
    
    def _aggregate_medical_features(self, medical_df: DataFrame) -> DataFrame:
        """Aggregate medical claims features by patient."""
        
        medical_agg = (
            medical_df.groupBy("patient_id")
            .agg(
                # Basic counts and costs
                count("*").alias("medical_claim_count"),
                spark_sum("paid_amount").alias("total_medical_cost"),
                avg("paid_amount").alias("avg_medical_cost"),
                stddev("paid_amount").alias("std_medical_cost"),
                spark_max("paid_amount").alias("max_medical_cost"),
                
                # Temporal patterns
                countDistinct("service_date").alias("unique_service_dates"),
                countDistinct("year").alias("years_with_claims"),
                countDistinct("month").alias("months_with_claims"),
                
                # Provider patterns
                countDistinct("provider_id").alias("unique_providers"),
                countDistinct("provider_specialty").alias("unique_specialties"),
                spark_sum(when(col("is_specialist"), 1).otherwise(0)).alias("specialist_visits"),
                
                # Diagnosis patterns
                countDistinct("icd10_primary").alias("unique_primary_diagnoses"),
                avg("secondary_diagnosis_count").alias("avg_secondary_diagnoses"),
                avg("complexity_score").alias("avg_complexity_score"),
                
                # Service settings
                countDistinct("place_of_service").alias("unique_service_locations"),
                spark_sum(when(col("service_setting") == "hospital", 1).otherwise(0)).alias("hospital_visits"),
                spark_sum(when(col("service_setting") == "office", 1).otherwise(0)).alias("office_visits"),
                
                # Cost patterns
                avg("cost_ratio").alias("avg_cost_ratio"),
                spark_sum(when(col("cost_category") == "high", 1).otherwise(0)).alias("high_cost_claims"),
                
                # Collect lists for further analysis
                collect_set("icd10_primary").alias("all_primary_diagnoses"),
                collect_set("provider_specialty").alias("all_provider_specialties")
            )
        )
        
        # Calculate derived features
        medical_agg = medical_agg.withColumn("medical_cost_per_claim",
                                           col("total_medical_cost") / col("medical_claim_count"))
        
        medical_agg = medical_agg.withColumn("specialist_visit_ratio",
                                           col("specialist_visits") / col("medical_claim_count"))
        
        return medical_agg
    
    def _aggregate_pharmacy_features(self, pharmacy_df: DataFrame) -> DataFrame:
        """Aggregate pharmacy claims features by patient."""
        
        pharmacy_agg = (
            pharmacy_df.groupBy("patient_id")
            .agg(
                # Basic counts and costs
                count("*").alias("pharmacy_claim_count"),
                spark_sum("paid_amount").alias("total_pharmacy_cost"),
                avg("paid_amount").alias("avg_pharmacy_cost"),
                stddev("paid_amount").alias("std_pharmacy_cost"),
                
                # Medication patterns
                countDistinct("ndc_code").alias("unique_medications"),
                countDistinct("generic_name").alias("unique_generic_drugs"),
                countDistinct("therapeutic_class").alias("unique_drug_classes"),
                
                # Temporal patterns
                countDistinct("fill_date").alias("unique_fill_dates"),
                avg("days_supply").alias("avg_days_supply"),
                
                # Adherence patterns
                avg("adherence_ratio").alias("avg_adherence_ratio"),
                stddev("adherence_ratio").alias("std_adherence_ratio"),
                
                # Chronic medication usage
                spark_sum(when(col("is_chronic_medication"), 1).otherwise(0)).alias("chronic_medication_fills"),
                
                # Cost patterns
                avg("cost_per_day").alias("avg_cost_per_day"),
                avg("daily_dose").alias("avg_daily_dose"),
                
                # Collect lists for further analysis
                collect_set("therapeutic_class").alias("all_therapeutic_classes"),
                collect_set("generic_name").alias("all_medications")
            )
        )
        
        # Calculate derived features
        pharmacy_agg = pharmacy_agg.withColumn("pharmacy_cost_per_claim",
                                             col("total_pharmacy_cost") / col("pharmacy_claim_count"))
        
        pharmacy_agg = pharmacy_agg.withColumn("chronic_medication_ratio",
                                             col("chronic_medication_fills") / col("pharmacy_claim_count"))
        
        return pharmacy_agg
