"""
Integration tests for the complete genetic testing pipeline.
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, DateType, DoubleType, IntegerType

from src.main import GeneticTestingPipeline


@pytest.fixture(scope="session")
def spark():
    """Create Spark session for testing."""
    return SparkSession.builder \
        .appName("GeneticTestingIntegrationTests") \
        .master("local[2]") \
        .config("spark.sql.adaptive.enabled", "false") \
        .getOrCreate()


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    config = Mock()
    config.settings.lookback_days = 365
    config.settings.min_claims_threshold = 1
    config.settings.similarity_threshold = 0.5
    config.settings.risk_threshold = 0.4
    config.model_config.similarity_method = "cosine"
    config.model_config.n_clusters = 3
    config.model_config.risk_model_type = "xgboost"
    
    # Mock table names
    config.get_table_name.side_effect = lambda x: f"test_{x}"
    config.get_spark_config.return_value = {
        "spark.sql.adaptive.enabled": "false"
    }
    
    return config


@pytest.fixture
def sample_test_data(spark):
    """Create comprehensive test dataset."""
    
    # Patients
    patients_data = [
        ("P001", "1980-01-01", "M", "White", "12345"),
        ("P002", "1975-05-15", "F", "Black", "12346"),
        ("P003", "1990-03-20", "F", "Hispanic", "12347"),
        ("P004", "1985-07-10", "M", "Asian", "12348"),
        ("P005", "1970-12-25", "F", "White", "12349"),
    ]
    
    patients_schema = StructType([
        StructField("patient_id", StringType(), False),
        StructField("date_of_birth", StringType(), True),
        StructField("gender", StringType(), True),
        StructField("race", StringType(), True),
        StructField("zip_code", StringType(), True),
    ])
    
    patients_df = spark.createDataFrame(patients_data, patients_schema)
    
    # Medical claims with genetic and non-genetic diagnoses
    medical_claims_data = [
        ("C001", "P001", "2023-01-15", "Z80.0", ["Z80.1"], 500.0, 450.0),  # Genetic
        ("C002", "P001", "2023-02-15", "I10", [], 200.0, 180.0),           # Non-genetic
        ("C003", "P002", "2023-01-20", "Q90.0", [], 2000.0, 1800.0),       # Genetic
        ("C004", "P003", "2023-01-25", "E70.0", ["E71.0"], 1500.0, 1350.0), # Genetic
        ("C005", "P004", "2023-02-01", "J44.0", [], 300.0, 270.0),         # Non-genetic
        ("C006", "P005", "2023-02-05", "Z82.3", [], 400.0, 360.0),         # Genetic
        ("C007", "P004", "2023-02-10", "I25.0", [], 800.0, 720.0),         # Non-genetic
        ("C008", "P005", "2023-02-15", "E78.0", [], 250.0, 225.0),         # Genetic
    ]
    
    medical_claims_schema = StructType([
        StructField("claim_id", StringType(), False),
        StructField("patient_id", StringType(), False),
        StructField("service_date", StringType(), False),
        StructField("icd10_primary", StringType(), True),
        StructField("icd10_secondary", StringType(), True),
        StructField("claim_amount", DoubleType(), True),
        StructField("paid_amount", DoubleType(), True),
    ])
    
    medical_claims_df = spark.createDataFrame(medical_claims_data, medical_claims_schema)
    
    # Pharmacy claims
    pharmacy_claims_data = [
        ("R001", "P001", "2023-01-20", "**********1", "Metformin", 30, 100.0, 90.0),
        ("R002", "P002", "2023-01-25", "**********2", "Lisinopril", 30, 50.0, 45.0),
        ("R003", "P003", "2023-01-30", "**********3", "Atorvastatin", 30, 150.0, 135.0),
        ("R004", "P004", "2023-02-05", "**********4", "Albuterol", 30, 75.0, 67.5),
        ("R005", "P005", "2023-02-10", "**********5", "Insulin", 30, 200.0, 180.0),
    ]
    
    pharmacy_claims_schema = StructType([
        StructField("claim_id", StringType(), False),
        StructField("patient_id", StringType(), False),
        StructField("fill_date", StringType(), False),
        StructField("ndc_code", StringType(), False),
        StructField("drug_name", StringType(), True),
        StructField("days_supply", IntegerType(), True),
        StructField("claim_amount", DoubleType(), True),
        StructField("paid_amount", DoubleType(), True),
    ])
    
    pharmacy_claims_df = spark.createDataFrame(pharmacy_claims_data, pharmacy_claims_schema)
    
    return {
        "patients": patients_df,
        "medical_claims": medical_claims_df,
        "pharmacy_claims": pharmacy_claims_df
    }


class TestGeneticTestingPipeline:
    """Integration tests for the complete pipeline."""
    
    @patch('src.main.get_config')
    def test_pipeline_initialization(self, mock_get_config, mock_config):
        """Test pipeline initialization."""
        mock_get_config.return_value = mock_config
        
        pipeline = GeneticTestingPipeline()
        
        assert pipeline.config == mock_config
        assert pipeline.spark is not None
        assert pipeline.data_loader is not None
        assert pipeline.icd_classifier is not None
    
    @patch('src.main.get_config')
    @patch('src.data.loaders.DataLoader.load_patients')
    @patch('src.data.loaders.DataLoader.load_medical_claims')
    @patch('src.data.loaders.DataLoader.load_pharmacy_claims')
    def test_data_loading_and_processing(self, 
                                       mock_load_pharmacy, 
                                       mock_load_medical, 
                                       mock_load_patients,
                                       mock_get_config, 
                                       mock_config, 
                                       sample_test_data):
        """Test data loading and processing step."""
        mock_get_config.return_value = mock_config
        
        # Mock data loading
        mock_load_patients.return_value = sample_test_data["patients"]
        mock_load_medical.return_value = sample_test_data["medical_claims"]
        mock_load_pharmacy.return_value = sample_test_data["pharmacy_claims"]
        
        pipeline = GeneticTestingPipeline()
        
        # Test data loading
        data_results = pipeline._load_and_process_data(365)
        
        assert "patients" in data_results
        assert "medical_claims" in data_results
        assert "pharmacy_claims" in data_results
        assert "patient_features" in data_results
        assert "summary" in data_results
        
        # Check summary statistics
        summary = data_results["summary"]
        assert summary["total_patients"] == 5
        assert summary["total_medical_claims"] == 8
        assert summary["total_pharmacy_claims"] == 5
    
    @patch('src.main.get_config')
    def test_genetic_patient_identification(self, mock_get_config, mock_config, sample_test_data):
        """Test genetic patient identification step."""
        mock_get_config.return_value = mock_config
        
        pipeline = GeneticTestingPipeline()
        
        # Mock data results
        data_results = {
            "medical_claims": sample_test_data["medical_claims"],
            "patients": sample_test_data["patients"]
        }
        
        genetic_results = pipeline._identify_genetic_patients(data_results)
        
        assert "genetic_patients" in genetic_results
        assert "count" in genetic_results
        
        # Should identify patients with genetic codes (P001, P002, P003, P005)
        assert genetic_results["count"] >= 3  # At least 3 patients with genetic diagnoses
    
    @patch('src.main.get_config')
    def test_end_to_end_pipeline_execution(self, mock_get_config, mock_config, sample_test_data):
        """Test complete pipeline execution with mocked data."""
        mock_get_config.return_value = mock_config
        
        # Create a minimal pipeline that doesn't require external data
        with patch.multiple(
            'src.data.loaders.DataLoader',
            load_patients=Mock(return_value=sample_test_data["patients"]),
            load_medical_claims=Mock(return_value=sample_test_data["medical_claims"]),
            load_pharmacy_claims=Mock(return_value=sample_test_data["pharmacy_claims"])
        ):
            pipeline = GeneticTestingPipeline()
            
            # Run pipeline with small dataset
            results = pipeline.run_full_pipeline(lookback_days=365, max_recommendations=10)
            
            # Check basic pipeline completion
            assert "pipeline_status" in results
            assert "execution_time_seconds" in results
            
            # If successful, check key metrics
            if results["pipeline_status"] == "completed_successfully":
                assert "genetic_patients_identified" in results
                assert "similar_patients_found" in results
                assert results["genetic_patients_identified"] > 0
    
    def test_pipeline_error_handling(self, mock_config):
        """Test pipeline error handling."""
        
        with patch('src.main.get_config', return_value=mock_config):
            pipeline = GeneticTestingPipeline()
            
            # Mock a failure in data loading
            with patch.object(pipeline.data_loader, 'load_patients', side_effect=Exception("Data loading failed")):
                results = pipeline.run_full_pipeline()
                
                assert results["pipeline_status"] == "failed"
                assert "error_message" in results
                assert "Data loading failed" in results["error_message"]
    
    def test_results_saving(self, mock_config):
        """Test saving of pipeline results."""
        
        with patch('src.main.get_config', return_value=mock_config):
            pipeline = GeneticTestingPipeline()
            
            # Mock results
            mock_results = {
                "pipeline_status": "completed_successfully",
                "execution_time_seconds": 120.5,
                "genetic_patients_identified": 10,
                "recommendations_generated": 25
            }
            
            with tempfile.TemporaryDirectory() as temp_dir:
                pipeline.save_results(mock_results, temp_dir)
                
                # Check that summary file was created
                summary_file = os.path.join(temp_dir, "pipeline_summary.json")
                assert os.path.exists(summary_file)
                
                # Read and verify summary content
                import json
                with open(summary_file, 'r') as f:
                    saved_summary = json.load(f)
                
                assert saved_summary["pipeline_status"] == "completed_successfully"
                assert saved_summary["genetic_patients_identified"] == 10


class TestPipelinePerformance:
    """Performance tests for the pipeline."""
    
    @patch('src.main.get_config')
    def test_pipeline_performance_with_larger_dataset(self, mock_get_config, mock_config, spark):
        """Test pipeline performance with larger synthetic dataset."""
        mock_get_config.return_value = mock_config
        
        # Generate larger synthetic dataset
        import random
        
        # Generate 1000 patients
        patients_data = [
            (f"P{i:04d}", f"198{random.randint(0,9)}-{random.randint(1,12):02d}-{random.randint(1,28):02d}", 
             random.choice(["M", "F"]), random.choice(["White", "Black", "Hispanic", "Asian"]), 
             f"{random.randint(10000,99999)}")
            for i in range(1000)
        ]
        
        patients_schema = StructType([
            StructField("patient_id", StringType(), False),
            StructField("date_of_birth", StringType(), True),
            StructField("gender", StringType(), True),
            StructField("race", StringType(), True),
            StructField("zip_code", StringType(), True),
        ])
        
        large_patients_df = spark.createDataFrame(patients_data, patients_schema)
        
        # Generate medical claims (some genetic, some not)
        genetic_codes = ["Z80.0", "Z80.1", "Q90.0", "E70.0", "Z82.3"]
        non_genetic_codes = ["I10", "J44.0", "I25.0", "K21.0", "M79.0"]
        
        claims_data = []
        for i in range(5000):  # 5000 claims
            patient_id = f"P{random.randint(0,999):04d}"
            is_genetic = random.random() < 0.1  # 10% genetic claims
            icd_code = random.choice(genetic_codes if is_genetic else non_genetic_codes)
            
            claims_data.append((
                f"C{i:06d}", patient_id, "2023-01-15", icd_code, [],
                random.uniform(100, 2000), random.uniform(90, 1800)
            ))
        
        medical_claims_schema = StructType([
            StructField("claim_id", StringType(), False),
            StructField("patient_id", StringType(), False),
            StructField("service_date", StringType(), False),
            StructField("icd10_primary", StringType(), True),
            StructField("icd10_secondary", StringType(), True),
            StructField("claim_amount", DoubleType(), True),
            StructField("paid_amount", DoubleType(), True),
        ])
        
        large_medical_claims_df = spark.createDataFrame(claims_data, medical_claims_schema)
        
        # Mock minimal pharmacy claims
        pharmacy_data = [(f"R{i:06d}", f"P{random.randint(0,999):04d}", "2023-01-20", 
                         f"**********{i%10}", "Drug", 30, 100.0, 90.0) for i in range(1000)]
        
        pharmacy_schema = StructType([
            StructField("claim_id", StringType(), False),
            StructField("patient_id", StringType(), False),
            StructField("fill_date", StringType(), False),
            StructField("ndc_code", StringType(), False),
            StructField("drug_name", StringType(), True),
            StructField("days_supply", IntegerType(), True),
            StructField("claim_amount", DoubleType(), True),
            StructField("paid_amount", DoubleType(), True),
        ])
        
        large_pharmacy_claims_df = spark.createDataFrame(pharmacy_data, pharmacy_schema)
        
        # Test pipeline with larger dataset
        with patch.multiple(
            'src.data.loaders.DataLoader',
            load_patients=Mock(return_value=large_patients_df),
            load_medical_claims=Mock(return_value=large_medical_claims_df),
            load_pharmacy_claims=Mock(return_value=large_pharmacy_claims_df)
        ):
            pipeline = GeneticTestingPipeline()
            
            import time
            start_time = time.time()
            
            # Run just the data processing step for performance testing
            data_results = pipeline._load_and_process_data(365)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Performance assertions
            assert execution_time < 60  # Should complete within 60 seconds
            assert data_results["summary"]["total_patients"] == 1000
            assert data_results["summary"]["total_medical_claims"] == 5000
            
            print(f"Performance test completed in {execution_time:.2f} seconds")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
