"""
Example script demonstrating genetic testing pipeline with HealthVerity data.
This script shows how to identify genetic testing candidates from your diagnosis data.
"""

import sys
import os
from pyspark.sql import SparkSession
from pyspark.sql.functions import *

# Add source path
sys.path.append('../src')

def main():
    """Main example demonstrating genetic testing pipeline."""
    
    print("🧬 HealthVerity Genetic Testing Pipeline Example")
    print("=" * 55)
    
    # Initialize Spark session
    spark = SparkSession.builder \
        .appName("HealthVerityGeneticTestingExample") \
        .config("spark.sql.adaptive.enabled", "true") \
        .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
        .getOrCreate()
    
    try:
        # Step 1: Load and explore data
        print("\n📊 Step 1: Loading HealthVerity diagnosis data...")
        
        from data.healthverity_loader import HealthVerityDataLoader
        
        loader = HealthVerityDataLoader(spark)
        
        # Explore table structure first
        print("🔍 Exploring table structure...")
        structure_info = loader.explore_table_structure("diagnosis")
        
        if "error" in structure_info:
            print(f"❌ Error exploring table: {structure_info['error']}")
            return
        
        print(f"✅ Table loaded successfully!")
        print(f"   Total rows: {structure_info['total_rows']:,}")
        print(f"   Total columns: {structure_info['total_columns']}")
        print(f"   Potential ICD columns: {structure_info['potential_icd_columns']}")
        
        # Show genetic patterns found
        genetic_found = structure_info['genetic_patterns_found']
        total_genetic = sum(genetic_found.values())
        print(f"   Genetic patterns found: {total_genetic:,} records")
        
        if total_genetic == 0:
            print("⚠️  No genetic patterns found with standard search. Trying broader search...")
        
        # Step 2: Identify genetic patients
        print("\n🧬 Step 2: Identifying patients with genetic diagnoses...")
        
        genetic_patients = loader.identify_genetic_patients_healthverity(
            lookback_days=365,
            min_genetic_claims=1
        )
        
        genetic_count = genetic_patients.count()
        print(f"✅ Found {genetic_count:,} patients with genetic diagnoses")
        
        if genetic_count > 0:
            print("\n📋 Sample genetic patients:")
            genetic_patients.limit(10).show(truncate=False)
            
            # Analyze genetic categories
            print("\n📊 Genetic diagnosis analysis:")
            
            # Most common genetic ICD codes
            genetic_codes_analysis = (
                genetic_patients
                .select(explode("genetic_icd_codes").alias("icd_code"))
                .groupBy("icd_code")
                .count()
                .orderBy(desc("count"))
                .limit(10)
            )
            
            print("Top 10 genetic ICD codes:")
            genetic_codes_analysis.show()
            
        else:
            print("⚠️  No genetic patients found. This could mean:")
            print("   1. The ICD code column mapping needs adjustment")
            print("   2. The genetic code patterns need to be expanded")
            print("   3. The data doesn't contain genetic diagnoses")
            print("\n💡 Try running the exploration notebook to identify correct mappings")
            return
        
        # Step 3: Load additional claims data (if available)
        print("\n📊 Step 3: Checking for additional claims data...")
        
        # Check what other tables are available
        available_tables = ["medical_claims", "pharmacy_claims", "patients"]
        
        for table_name in available_tables:
            try:
                table_info = loader.explore_table_structure(table_name)
                if "error" not in table_info:
                    print(f"✅ {table_name}: {table_info['total_rows']:,} rows")
                else:
                    print(f"❌ {table_name}: Not available")
            except:
                print(f"❌ {table_name}: Not available")
        
        # Step 4: Basic patient profiling
        print("\n👥 Step 4: Basic patient profiling...")
        
        # Load full diagnosis data for genetic patients
        genetic_patient_ids = [row.patient_id for row in genetic_patients.select("patient_id").collect()]
        
        if len(genetic_patient_ids) > 0:
            # Get all diagnoses for genetic patients
            all_diagnoses = loader.load_diagnosis_data(
                patient_ids=genetic_patient_ids[:100]  # Limit to first 100 for demo
            )
            
            # Analyze diagnosis patterns
            diagnosis_patterns = (
                all_diagnoses
                .groupBy("patient_id")
                .agg(
                    count("*").alias("total_diagnoses"),
                    countDistinct("icd_code").alias("unique_diagnoses"),
                    collect_set("icd_code").alias("all_icd_codes")
                )
            )
            
            print("📊 Diagnosis patterns for genetic patients:")
            diagnosis_patterns.select("patient_id", "total_diagnoses", "unique_diagnoses").show()
            
            # Calculate average utilization
            avg_stats = diagnosis_patterns.select(
                avg("total_diagnoses").alias("avg_total_diagnoses"),
                avg("unique_diagnoses").alias("avg_unique_diagnoses")
            ).collect()[0]
            
            print(f"📈 Average utilization patterns:")
            print(f"   Average total diagnoses per patient: {avg_stats.avg_total_diagnoses:.2f}")
            print(f"   Average unique diagnoses per patient: {avg_stats.avg_unique_diagnoses:.2f}")
        
        # Step 5: Identify potential candidates
        print("\n🎯 Step 5: Identifying potential genetic testing candidates...")
        
        # For this example, we'll identify patients with high utilization but no genetic diagnoses
        all_patients_utilization = (
            loader.load_diagnosis_data(limit=10000)  # Sample for demo
            .groupBy("patient_id")
            .agg(
                count("*").alias("total_diagnoses"),
                countDistinct("icd_code").alias("unique_diagnoses")
            )
            .filter(col("total_diagnoses") >= 5)  # High utilization threshold
        )
        
        # Exclude patients who already have genetic diagnoses
        potential_candidates = (
            all_patients_utilization
            .filter(~col("patient_id").isin(genetic_patient_ids))
            .orderBy(desc("total_diagnoses"))
            .limit(20)
        )
        
        candidate_count = potential_candidates.count()
        print(f"✅ Found {candidate_count} potential genetic testing candidates")
        
        if candidate_count > 0:
            print("\n📋 Top potential candidates (high utilization, no genetic diagnosis):")
            potential_candidates.show()
        
        # Step 6: Generate summary report
        print("\n📊 Step 6: Summary Report")
        print("=" * 30)
        
        total_patients = loader.load_diagnosis_data().select("patient_id").distinct().count()
        genetic_prevalence = (genetic_count / total_patients) * 100 if total_patients > 0 else 0
        
        print(f"📈 Key Metrics:")
        print(f"   Total patients in dataset: {total_patients:,}")
        print(f"   Patients with genetic diagnoses: {genetic_count:,}")
        print(f"   Genetic diagnosis prevalence: {genetic_prevalence:.2f}%")
        print(f"   Potential testing candidates identified: {candidate_count:,}")
        
        # Step 7: Next steps recommendations
        print("\n🎯 Recommended Next Steps:")
        print("=" * 30)
        
        if genetic_count > 50:
            print("✅ Sufficient genetic patients for ML model development")
            print("   → Proceed with patient profiling and similarity matching")
            print("   → Train risk scoring models")
            print("   → Generate comprehensive recommendations")
        else:
            print("⚠️  Limited genetic patients found")
            print("   → Review and expand genetic code patterns")
            print("   → Consider broader lookback period")
            print("   → Validate ICD code column mapping")
        
        if candidate_count > 100:
            print("✅ Good pool of potential candidates")
            print("   → Apply ML similarity matching")
            print("   → Prioritize by risk scores")
        else:
            print("⚠️  Limited candidate pool")
            print("   → Lower utilization thresholds")
            print("   → Expand candidate criteria")
        
        print("\n📚 Resources:")
        print("   → Run notebooks/healthverity_data_exploration.py for detailed analysis")
        print("   → See docs/API_REFERENCE.md for full pipeline capabilities")
        print("   → Use src/main.py for complete ML pipeline execution")
        
        print("\n🎉 Example completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running example: {str(e)}")
        print("💡 Troubleshooting tips:")
        print("   1. Ensure you have access to the HealthVerity tables")
        print("   2. Check that column mappings are correct")
        print("   3. Verify Databricks permissions")
        print("   4. Run the setup script first: python setup_healthverity.py")
        
    finally:
        spark.stop()

if __name__ == "__main__":
    main()
