# Genetic Testing ML Pipeline

A Databricks-based machine learning pipeline to identify patients who may benefit from genetic testing based on claims history analysis.

## Overview

This system:
1. Identifies patients with genetic disorder diagnoses (ICD-10 Z80-Z84)
2. Analyzes their medical and pharmacy claims patterns
3. Builds patient profiles of common claims
4. Identifies similar undiagnosed patients for genetic testing recommendations

## Project Structure

```
genetic_testing/
├── src/
│   ├── data/
│   │   ├── schemas.py          # Data schemas and validation
│   │   ├── loaders.py          # Data loading utilities
│   │   └── processors.py       # Data processing pipelines
│   ├── models/
│   │   ├── icd_classifier.py   # ICD-10 genetic disorder classification
│   │   ├── profile_builder.py  # Patient profile generation
│   │   ├── similarity.py       # Similarity matching algorithms
│   │   └── recommender.py      # Risk scoring and recommendations
│   ├── utils/
│   │   ├── config.py           # Configuration management
│   │   ├── logging.py          # Logging utilities
│   │   └── helpers.py          # Helper functions
│   └── main.py                 # Main pipeline orchestration
├── tests/
│   ├── test_data/              # Test data fixtures
│   ├── test_models/            # Model tests
│   └── test_integration/       # Integration tests
├── notebooks/
│   ├── exploration/            # Data exploration notebooks
│   ├── model_development/      # Model development notebooks
│   └── validation/             # Model validation notebooks
├── config/
│   ├── databricks_config.yaml # Databricks configuration
│   └── model_config.yaml      # Model parameters
└── requirements.txt           # Python dependencies
```

## Key Features

- **ICD-10 Classification**: Automated identification of genetic disorder patients
- **Claims Analysis**: Comprehensive medical and pharmacy claims processing
- **ML-based Profiling**: Advanced patient profiling using machine learning
- **Similarity Matching**: Intelligent matching of undiagnosed patients
- **Risk Scoring**: Probabilistic risk assessment for genetic testing recommendations
- **Scalable Architecture**: Designed for large healthcare datasets in Databricks

## Getting Started

1. Set up Databricks environment
2. Install dependencies: `pip install -r requirements.txt`
3. Configure data sources in `config/databricks_config.yaml`
4. Run the main pipeline: `python src/main.py`

## Data Requirements

- Patient demographics
- Medical claims with ICD-10 codes
- Pharmacy claims
- Provider information (optional)

## Compliance

This system is designed with healthcare data privacy and compliance in mind. Ensure proper data governance and HIPAA compliance when deploying.
