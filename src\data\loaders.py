"""
Data loading utilities for genetic testing ML pipeline.
Handles loading data from Databricks tables and external sources.
"""

import logging
from typing import Optional, List, Dict, Any
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import col, when, isnan, isnull, count
from databricks.sql import connect
import pandas as pd

from ..utils.config import get_config
from .schemas import (
    PATIENT_SCHEMA, MEDICAL_CLAIMS_SCHEMA, 
    PHARMACY_CLAIMS_SCHEMA, ICD10_CODES_SCHEMA
)

logger = logging.getLogger(__name__)


class DataLoader:
    """Main data loader for genetic testing pipeline."""
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.config = get_config()
        
    def load_patients(self, 
                     filter_conditions: Optional[str] = None,
                     limit: Optional[int] = None) -> DataFrame:
        """
        Load patient demographic data.
        
        Args:
            filter_conditions: SQL WHERE clause conditions
            limit: Maximum number of records to return
            
        Returns:
            Spark DataFrame with patient data
        """
        table_name = self.config.get_table_name("patients")
        
        query = f"SELECT * FROM {table_name}"
        
        if filter_conditions:
            query += f" WHERE {filter_conditions}"
            
        if limit:
            query += f" LIMIT {limit}"
            
        logger.info(f"Loading patients with query: {query}")
        
        df = self.spark.sql(query)
        
        # Validate schema
        self._validate_schema(df, PATIENT_SCHEMA, "patients")
        
        return df
    
    def load_medical_claims(self,
                           patient_ids: Optional[List[str]] = None,
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None,
                           icd_codes: Optional[List[str]] = None) -> DataFrame:
        """
        Load medical claims data with optional filtering.
        
        Args:
            patient_ids: List of patient IDs to filter
            start_date: Start date for service_date filter (YYYY-MM-DD)
            end_date: End date for service_date filter (YYYY-MM-DD)
            icd_codes: List of ICD-10 codes to filter
            
        Returns:
            Spark DataFrame with medical claims
        """
        table_name = self.config.get_table_name("medical_claims")
        
        df = self.spark.table(table_name)
        
        # Apply filters
        if patient_ids:
            df = df.filter(col("patient_id").isin(patient_ids))
            
        if start_date:
            df = df.filter(col("service_date") >= start_date)
            
        if end_date:
            df = df.filter(col("service_date") <= end_date)
            
        if icd_codes:
            # Filter by primary or secondary ICD codes
            icd_filter = (
                col("icd10_primary").isin(icd_codes) |
                col("icd10_secondary").rlike("|".join(icd_codes))
            )
            df = df.filter(icd_filter)
        
        logger.info(f"Loaded {df.count()} medical claims")
        
        # Validate schema
        self._validate_schema(df, MEDICAL_CLAIMS_SCHEMA, "medical_claims")
        
        return df
    
    def load_pharmacy_claims(self,
                            patient_ids: Optional[List[str]] = None,
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None,
                            drug_classes: Optional[List[str]] = None) -> DataFrame:
        """
        Load pharmacy claims data with optional filtering.
        
        Args:
            patient_ids: List of patient IDs to filter
            start_date: Start date for fill_date filter (YYYY-MM-DD)
            end_date: End date for fill_date filter (YYYY-MM-DD)
            drug_classes: List of therapeutic classes to filter
            
        Returns:
            Spark DataFrame with pharmacy claims
        """
        table_name = self.config.get_table_name("pharmacy_claims")
        
        df = self.spark.table(table_name)
        
        # Apply filters
        if patient_ids:
            df = df.filter(col("patient_id").isin(patient_ids))
            
        if start_date:
            df = df.filter(col("fill_date") >= start_date)
            
        if end_date:
            df = df.filter(col("fill_date") <= end_date)
            
        if drug_classes:
            df = df.filter(col("therapeutic_class").isin(drug_classes))
        
        logger.info(f"Loaded {df.count()} pharmacy claims")
        
        # Validate schema
        self._validate_schema(df, PHARMACY_CLAIMS_SCHEMA, "pharmacy_claims")
        
        return df
    
    def load_icd10_codes(self, genetic_only: bool = False) -> DataFrame:
        """
        Load ICD-10 code reference data.
        
        Args:
            genetic_only: If True, only return genetic-related codes
            
        Returns:
            Spark DataFrame with ICD-10 codes
        """
        table_name = self.config.get_table_name("icd10_codes")
        
        df = self.spark.table(table_name)
        
        if genetic_only:
            df = df.filter(col("is_genetic_related") == True)
        
        logger.info(f"Loaded {df.count()} ICD-10 codes")
        
        return df
    
    def load_genetic_patients(self, 
                             lookback_days: Optional[int] = None) -> DataFrame:
        """
        Load patients with genetic disorder diagnoses.
        
        Args:
            lookback_days: Number of days to look back for diagnoses
            
        Returns:
            Spark DataFrame with genetic patients and their diagnoses
        """
        if lookback_days is None:
            lookback_days = self.config.settings.lookback_days
        
        # Get genetic ICD codes
        genetic_codes_df = self.load_icd10_codes(genetic_only=True)
        genetic_codes = [row.icd10_code for row in genetic_codes_df.collect()]
        
        # Load medical claims with genetic diagnoses
        end_date = "current_date()"
        start_date = f"date_sub(current_date(), {lookback_days})"
        
        medical_claims = self.load_medical_claims(
            start_date=start_date,
            end_date=end_date,
            icd_codes=genetic_codes
        )
        
        # Get unique patients with genetic diagnoses
        genetic_patients = (
            medical_claims
            .select("patient_id", "icd10_primary", "service_date")
            .filter(col("icd10_primary").isin(genetic_codes))
            .groupBy("patient_id")
            .agg(
                count("*").alias("genetic_claim_count"),
                collect_list("icd10_primary").alias("genetic_icd_codes"),
                min("service_date").alias("first_genetic_diagnosis"),
                max("service_date").alias("last_genetic_diagnosis")
            )
        )
        
        logger.info(f"Found {genetic_patients.count()} patients with genetic diagnoses")
        
        return genetic_patients
    
    def _validate_schema(self, df: DataFrame, expected_schema, table_name: str):
        """Validate DataFrame schema against expected schema."""
        df_fields = {field.name: field.dataType for field in df.schema.fields}
        expected_fields = {field.name: field.dataType for field in expected_schema.fields}
        
        missing_fields = set(expected_fields.keys()) - set(df_fields.keys())
        if missing_fields:
            logger.warning(f"Missing fields in {table_name}: {missing_fields}")
        
        extra_fields = set(df_fields.keys()) - set(expected_fields.keys())
        if extra_fields:
            logger.info(f"Extra fields in {table_name}: {extra_fields}")
    
    def get_data_quality_report(self, df: DataFrame, table_name: str) -> Dict[str, Any]:
        """Generate data quality report for a DataFrame."""
        total_rows = df.count()
        
        # Calculate null percentages for each column
        null_counts = df.select([
            count(when(col(c).isNull() | isnan(c), c)).alias(c) 
            for c in df.columns
        ]).collect()[0].asDict()
        
        null_percentages = {
            col_name: (null_count / total_rows) * 100 
            for col_name, null_count in null_counts.items()
        }
        
        # Get basic statistics
        numeric_cols = [field.name for field in df.schema.fields 
                       if field.dataType.typeName() in ['integer', 'double', 'float']]
        
        stats = {}
        if numeric_cols:
            stats = df.select(numeric_cols).describe().collect()
        
        report = {
            "table_name": table_name,
            "total_rows": total_rows,
            "total_columns": len(df.columns),
            "null_percentages": null_percentages,
            "numeric_statistics": stats,
            "schema": df.schema.json()
        }
        
        logger.info(f"Data quality report for {table_name}: {total_rows} rows, "
                   f"{len(df.columns)} columns")
        
        return report


class ExternalDataLoader:
    """Loader for external data sources."""
    
    def __init__(self):
        self.config = get_config()
    
    def load_icd10_reference(self, file_path: str) -> pd.DataFrame:
        """Load ICD-10 reference data from external file."""
        logger.info(f"Loading ICD-10 reference data from {file_path}")
        
        # Assuming CSV format
        df = pd.read_csv(file_path)
        
        # Standardize column names
        column_mapping = {
            'code': 'icd10_code',
            'description': 'description',
            'category': 'category'
        }
        
        df = df.rename(columns=column_mapping)
        
        # Add genetic classification
        df['is_genetic_related'] = df['icd10_code'].str.startswith(('Z8', 'Q9', 'E7'))
        
        return df
    
    def load_drug_reference(self, file_path: str) -> pd.DataFrame:
        """Load drug reference data from external file."""
        logger.info(f"Loading drug reference data from {file_path}")
        
        df = pd.read_csv(file_path)
        
        # Standardize column names
        column_mapping = {
            'ndc': 'ndc_code',
            'drug_name': 'drug_name',
            'generic_name': 'generic_name',
            'therapeutic_class': 'therapeutic_class'
        }
        
        df = df.rename(columns=column_mapping)
        
        return df
